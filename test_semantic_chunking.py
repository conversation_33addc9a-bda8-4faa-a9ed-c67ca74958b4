#!/usr/bin/env python3
"""
Test script to demonstrate semantic chunking vs sentence-based processing.
This script simulates semantic chunking behavior for testing purposes.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from mrl_generator import MRLGenerator

def test_chunking_comparison():
    """Compare sentence-based vs simulated chunk-based processing."""
    
    # Test text with multiple related sentences that would benefit from chunking
    test_text = """
    Mechanical biliary obstruction results in raised levels of ALP. ALP will usually be markedly raised in comparison with ALT. 
    Elevated ALP suggests biliary obstruction. When due to biliary obstruction, levels of ALP and GGT tend to fluctuate.
    
    Hepatocellular damage causes increased levels of ALT and AST. ALT is more specific for liver damage than AST. 
    High ALT levels indicate hepatocyte injury.
    
    Chronic liver disease is associated with elevated bilirubin levels. Bilirubin elevation signifies impaired liver function. 
    Portal hypertension results from chronic liver disease.
    """
    
    print("=== Testing Sentence-based Processing ===")
    # Test with sentence-based processing (semantic chunking disabled)
    generator_sentences = MRLGenerator(use_llm=False, use_semantic_chunking=False)
    relations_sentences = generator_sentences.generate_for_document(test_text, "test_doc")
    
    print(f"Sentence-based processing found {len(relations_sentences)} relations:")
    for rel, text, chunk_id in relations_sentences:
        print(f"  Chunk {chunk_id}: {rel.predicate} relation from '{text[:50]}...'")
    
    print("\n=== Simulating Semantic Chunking ===")
    # Simulate semantic chunking by manually creating meaningful chunks
    semantic_chunks = [
        ("Mechanical biliary obstruction results in raised levels of ALP. ALP will usually be markedly raised in comparison with ALT. Elevated ALP suggests biliary obstruction. When due to biliary obstruction, levels of ALP and GGT tend to fluctuate.", 1),
        ("Hepatocellular damage causes increased levels of ALT and AST. ALT is more specific for liver damage than AST. High ALT levels indicate hepatocyte injury.", 2),
        ("Chronic liver disease is associated with elevated bilirubin levels. Bilirubin elevation signifies impaired liver function. Portal hypertension results from chronic liver disease.", 3)
    ]
    
    generator_chunks = MRLGenerator(use_llm=False, use_semantic_chunking=False)
    relations_chunks = []
    
    for chunk_text, chunk_id in semantic_chunks:
        chunk_relations = generator_chunks.extract_relations_heuristic(chunk_text, "test_doc", chunk_id)
        for rel in chunk_relations:
            relations_chunks.append((rel, chunk_text, chunk_id))
    
    print(f"Semantic chunking simulation found {len(relations_chunks)} relations:")
    for rel, text, chunk_id in relations_chunks:
        print(f"  Chunk {chunk_id}: {rel.predicate} relation from '{text[:50]}...'")
    
    print(f"\n=== Comparison ===")
    print(f"Sentence-based: {len(relations_sentences)} relations")
    print(f"Chunk-based: {len(relations_chunks)} relations")
    
    # Show the difference in context
    print("\n=== Context Comparison ===")
    print("Sentence-based chunks (first 3):")
    for i, (rel, text, chunk_id) in enumerate(relations_sentences[:3]):
        print(f"  {chunk_id}: '{text}'")
    
    print("\nSemantic chunks:")
    for chunk_text, chunk_id in semantic_chunks:
        print(f"  {chunk_id}: '{chunk_text[:100]}...'")

if __name__ == "__main__":
    test_chunking_comparison()
