"""
Medical Causal‑Knowledge Representation Language (MRL) Generator
================================================================

This module provides functionality for extracting causal and associative
relationships from unstructured biomedical text and encoding them using
the concrete syntax of the Medical Causal‑Knowledge Representation
Language (MRL).  MRL is a simple, line‑oriented language designed to
capture causal, evidential and taxonomic knowledge together with
provenance and confidence information.  The specification for MRL is
outlined in the accompanying documentation and summarised in the
internal comments of this file.

The generator implemented here applies a combination of pattern‑based
heuristics and optional large language model (LLM) prompts to
identify entities and relations.  The goal is to provide a generic
framework that can be used on diverse clinical and biomedical
literature—from PubMed abstracts to electronic health record (EHR)
notes—without being hard‑coded to any particular source.  By keeping
the heuristics transparent and configurable, the script is intended
to serve both as a baseline extractor and as a scaffold for more
sophisticated methods.

Key Features
-------------

* **Entity management:** The script maintains a registry of entities
  discovered in the input and assigns unique identifiers.  Where
  possible it can query an ontology (e.g. SNOMED CT) to attach a
  canonical URI.  A fallback identifier generator is provided when
  ontology lookup fails.

* **Relation extraction:** A suite of simple regular expressions is
  used to recognise common causal verbs ("causes", "results in",
  etc.), influence phrases ("raises", "decreases"), evidential
  phrases ("signifies", "suggests"), taxonomic patterns ("is a"),
  property associations ("associated with"), and temporal cues
  ("before", "after", "preceded by").  These patterns are not
  exhaustive but cover many typical expressions found in clinical
  narratives.  The design allows additional patterns to be added
  easily.

* **Probability and modality:** Each extracted relation is assigned a
  probability based on lexical cues (e.g. "always" yields a high
  probability, "may" yields a lower one).  This reflects the
  inherent uncertainty of medical knowledge and satisfies MRL's
  requirement for probabilistic annotations.

* **Qualifiers:** Direction (positive/negative), magnitude
  (marked/moderate/mild) and modality are captured as qualifiers
  whenever they can be parsed from the text.  These qualifiers
  conform to the MRL grammar and enrich the semantics of
  influence relations.

* **Provenance:** Each MRL statement includes a provenance block
  recording at minimum the source identifier of the originating
  document.  Optional fields such as agent name, timestamp and
  external reference can be added.  Confidence values can also be
  included here if required by downstream tools.

* **LLM integration:** A placeholder interface to OpenAI's API is
  included.  When an API key is provided, the generator will
  attempt to augment or replace the heuristic extraction with
  responses from a language model.  This is optional and the
  script operates entirely on heuristics if no API key is supplied.

Usage
-----

The main entry point is the :func:`generate_mrl` function.  It
takes a collection of (text, source_id) pairs and returns a list
of MRL statements as strings.  These statements can be written
directly to a file for consumption by an MRL parser or compiler.

Example::

    from mrl_generator import generate_mrl

    documents = [
        ("Mechanical biliary obstruction results in raised levels of ALP.", "PMID:12345"),
        ("ALP will usually be markedly raised in comparison with ALT.", "PMID:12345"),
    ]
    mrl_lines = generate_mrl(documents)
    for line in mrl_lines:
        print(line)

The above would produce entity definitions for "Mechanical biliary
obstruction", "ALP" and "ALT", followed by relation statements
representing the causal and influence assertions.

Note
----

This script is designed to be **domain‑agnostic**: it does not
contain any hard‑coded references to the example text provided in
the project specification.  All extraction logic operates on
arbitrary input and relies only on the structural cues contained
within the text itself.  When used in production, you may wish to
extend or replace the heuristic patterns with more sophisticated
natural language understanding components (such as trained NER
models, dependency parsers, or rule‑based systems).  The LLM
integration points provide a natural location for such enhancements.

"""

from __future__ import annotations

import re
import uuid
from dataclasses import dataclass, field
from datetime import datetime
from typing import Dict, List, Optional, Tuple
import os
import sys
import traceback
import json

try:
    import openai  # type: ignore
    from openai import Client
    client = Client()
    if os.getenv("OPENAI_API_KEY"):
        openai.api_key = os.getenv("OPENAI_API_KEY")
except Exception:
    # openai is optional; the script still functions without it
    openai = None  # type: ignore

###############################################################################
# Data Classes
###############################################################################

@dataclass
class Entity:
    """Represents a biomedical entity (e.g., disease, substance, lab test)."""
    name: str
    entity_type: str = "entity"  # one of {entity, finding, property}
    uri: Optional[str] = None
    id: Optional[str] = None

@dataclass
class Relation:
    """Represents a relational assertion between entities."""
    subject_id: str
    predicate: str
    object_id: str
    object_property_id: Optional[str] = None  # used for influences and comparative properties
    probability: float = 1.0
    qualifiers: Dict[str, str] = field(default_factory=dict)
    provenance: Dict[str, str] = field(default_factory=dict)
    # For comparative property relations, we need additional fields
    comparative_obj_id2: Optional[str] = None
    comparative_property_id1: Optional[str] = None
    comparative_property_id2: Optional[str] = None
    comparative_relation: Optional[str] = None

    def to_mrl(self) -> str:
        """Render the relation as a single MRL statement."""
        parts: List[str] = []
        # probability prefix
        if self.probability is not None:
            prob_str = f"{self.probability:.3f}::"
            parts.append(prob_str)
        # core assertion
        if self.predicate == "influences" and self.object_property_id:
            core = f"{self.subject_id} influences {self.object_id}.{self.object_property_id}"
        elif self.predicate == "is_a":
            core = f"{self.subject_id} is_a {self.object_id}"
        elif self.predicate == "has_property":
            # object_id may be an entity id or a quoted string
            core = f"{self.subject_id} has_property {self.object_id}"
        elif self.predicate == "is_evidence_for":
            core = f"{self.subject_id} is_evidence_for {self.object_id}"
        elif self.predicate == "has_temporal_relation":
            core = f"{self.subject_id} has_temporal_relation {self.object_id} "
            core += f"\"{self.qualifiers.get('temporal_relation','')}\""
        elif self.predicate == "condition_influences" and self.object_property_id:
            core = (
                f"condition({self.subject_id}) influences {self.object_id}.{self.object_property_id}"
            )
        elif self.predicate == "condition_has_comparative_property":
            # e.g., condition(X) has_comparative_property (A.prop, B.prop, relation)
            core = (
                f"condition({self.subject_id}) has_comparative_property ("
                f"{self.object_id}.{self.comparative_property_id1}, "
                f"{self.comparative_obj_id2}.{self.comparative_property_id2}, "
                f"\"{self.comparative_relation}\")"
            )
        else:
            # default: binary relation
            core = f"{self.subject_id} {self.predicate} {self.object_id}"
        parts.append(core)
        # qualifier block
        qualifier_entries = []
        for k, v in self.qualifiers.items():
            # skip temporal_relation here since it's appended in core for temporal
            if k == "temporal_relation":
                continue
            if isinstance(v, float):
                qualifier_entries.append(f"{k}={v:.3f}")
            else:
                qualifier_entries.append(f"{k}={v}")
        if qualifier_entries:
            parts.append("[" + ",".join(qualifier_entries) + "]")
        # provenance block
        prov_entries = []
        for k, v in self.provenance.items():
            # If the value contains whitespace or any quote/colon characters,
            # wrap it in double quotes.  Using a list of characters avoids
            # syntax issues with string literals containing quotes.
            special_chars = [':', '"', "'"]
            if any(ch.isspace() for ch in v) or any(ch in v for ch in special_chars):
                # Escape any existing double quotes in v
                escaped = v.replace('"', '\\"')
                prov_entries.append(f"{k}:\"{escaped}\"")
            else:
                prov_entries.append(f"{k}:{v}")
        if prov_entries:
            parts.append("{" + ",".join(prov_entries) + "}")
        return " ".join(parts)


###############################################################################
# Ontology Connector & ID Generation
###############################################################################

class OntologyConnector:
    """Stub for connecting to a biomedical ontology.

    In a production environment this class would query a local or remote
    terminology service (e.g. SNOMED CT, UMLS, OBO Foundry) to resolve
    entity names into globally unique URIs.  If a concept cannot be
    resolved, ``lookup`` returns ``None`` and the caller should fall back
    to generating a local identifier.
    """

    def lookup(self, term: str) -> Optional[str]:
        # Placeholder implementation: always return None.
        # You can extend this method to search an on‑disk ontology or call
        # an external service.  For demonstration, we simply return None.
        return None


class UniqueIDGenerator:
    """Generates unique identifiers for entities and properties."""

    def __init__(self, prefix: str = "E"):
        self.prefix = prefix
        self.count = 0

    def next_id(self) -> str:
        self.count += 1
        return f"{self.prefix}{self.count}"


###############################################################################
# Helper Functions
###############################################################################

def split_sentences(text: str) -> List[str]:
    """Splits text into sentences using simple punctuation heuristics.

    This function uses regular expressions to divide the input on
    sentence‑ending punctuation (periods, question marks, exclamation
    marks) while attempting to avoid splitting on common abbreviations.
    For high‑accuracy segmentation, consider using a NLP toolkit such
    as spaCy; however, this heuristic should suffice for many
    biomedical texts.
    """
    # Replace newlines with spaces to normalise paragraphs
    tmp = text.replace("\n", " ")
    # Basic abbreviation pattern to avoid splitting (e.g. "e.g.", "i.e.")
    abbrev_pattern = re.compile(r"\b(?:e\.g|i\.e|vs|mr|mrs|dr|prof)\.\s", re.IGNORECASE)
    # Temporary placeholder for abbreviations
    tmp = abbrev_pattern.sub(lambda m: m.group(0).replace(".", "<DOT>"), tmp)
    # Split on ., ? or ! followed by space and a capital letter or end of string
    sentences = re.split(r"(?<=[\.!?])\s+(?=[A-Z0-9])", tmp)
    # Restore dots in abbreviations
    sentences = [s.replace("<DOT>", ".").strip() for s in sentences if s.strip()]
    return sentences


def tokenize_entities(sentence: str) -> List[str]:
    """Extract candidate entity phrases from a sentence using a simple heuristic.

    This function identifies entities as sequences beginning with an
    uppercase word or an all‑caps acronym (including tokens with
    colons, such as "AST:ALT").  If an uppercase token is followed by
    one or more lower‑case tokens that are not stop words, those
    tokens are included as part of the entity name.  The goal is to
    capture multi‑word expressions like "Mechanical biliary obstruction"
    without erroneously grouping unrelated capitalised terms such as
    "ALP" and "GGT" separated by conjunctions or commas.
    """
    # Define stop words that terminate entity expansion
    stop_words = {
        # common conjunctions and prepositions
        'and', 'or', 'with', 'without', 'due', 'because', 'when', 'then', 'than',
        'in', 'on', 'at', 'to', 'of', 'for', 'by', 'from', 'between',
        # auxiliary and modal verbs
        'be', 'is', 'are', 'was', 'were', 'have', 'has', 'had', 'will', 'would',
        'should', 'could', 'may', 'might', 'must', 'can', 'cannot',
        # adverbs and qualifiers
        'often', 'usually', 'generally', 'typically', 'rarely', 'sometimes',
        'tend', 'tends', 'tending', 'tend', 'tends',
        # determiners
        'a', 'an', 'the',
        # negative words
        'not', 'no', 'none', 'neither', 'nor',
        # conjunctions
        'but', 'if', 'else', 'otherwise',
        # verbs that commonly follow entity names and should terminate them
        'results', 'result', 'causes', 'cause', 'leads', 'lead', 'induces', 'induce', 'triggers', 'trigger',
        'signifies', 'signify', 'suggests', 'suggest', 'indicates', 'indicate', 'points', 'point', 'implies', 'imply',
        'associated', 'associate', 'related', 'relate', 'linked', 'link', 'correlated', 'correlate',
        'elevated', 'raised', 'increased', 'decreased', 'lowered', 'reduced', 'suppressed', 'peaked', 'fluctuating', 'fluctuate', 'fluctuates',
        'assist', 'assists', 'assistive', 'differentiating', 'differentiates', 'differentiate', 'differentiating',
        # comparison words
        'higher', 'greater', 'lower', 'less', 'more',
        # units and non‑entity tokens
        'i/u', 'iu', 'u/i', 'international', 'units',
    }
    # Replace parentheses and some punctuation with spaces to avoid attaching them to words
    clean = re.sub(r"[\(\)\[\],;\.!?]", " ", sentence)
    tokens = [t for t in clean.split() if t]
    candidates: List[str] = []
    i = 0
    while i < len(tokens):
        tok = tokens[i]
        # Remove trailing punctuation
        tok_clean = tok.strip().strip(":,;.")
        # Identify starting tokens: begins with uppercase letter or fully uppercase, or contains colon (ratio)
        if re.match(r"^[A-Z][A-Za-z0-9:/\-]*$", tok_clean):
            # Skip if token itself is a stop word (e.g. 'When', 'The')
            if tok_clean.lower() in stop_words:
                i += 1
                continue
            phrase_tokens = [tok_clean]
            j = i + 1
            # Expand to include following lowercase tokens that are not stop words
            while j < len(tokens):
                next_tok = tokens[j].strip().strip(":,;.")
                if next_tok.lower() in stop_words:
                    break
                # Include if starts with lowercase letter or contains digits and letters
                if re.match(r"^[a-z][a-z0-9:/\-]*$", next_tok):
                    phrase_tokens.append(next_tok)
                    j += 1
                else:
                    break
            phrase = " ".join(phrase_tokens)
            # Split on conjunctions inside phrase (e.g., "ALP and GGT" becomes two entities)
            # If 'and' occurs within the phrase, treat preceding part as separate entity
            if ' and ' in phrase:
                # For example: "ALP and GGT" -> ["ALP", "GGT"]
                sub_parts = [p.strip() for p in phrase.split(' and ') if p.strip()]
                candidates.extend(sub_parts)
            else:
                candidates.append(phrase)
            i = j
        else:
            i += 1
    # Deduplicate while preserving order
    seen = set()
    unique_candidates: List[str] = []
    for c in candidates:
        if c not in seen:
            seen.add(c)
            unique_candidates.append(c)
    return unique_candidates


def compute_probability(sentence: str, predicate: str) -> float:
    """Estimate a probability/confidence value based on lexical cues.

    This simplistic function assigns a probability between 0 and 1
    according to the strength of modal verbs and adverbs found in the
    sentence.  It is not scientifically calibrated but serves the
    purpose of producing relative confidences required by MRL.
    """
    s = sentence.lower()
    # Assign base probabilities
    prob = 0.5
    # Strong causal cues
    if any(k in s for k in ["always", "definitely", "certainly", "will invariably"]):
        prob = 0.95
    elif any(k in s for k in ["results in", "leads to", "causes", "cause", "induces"]):
        prob = 0.9
    # Moderate cues
    elif any(k in s for k in ["usually", "often", "generally", "tends to", "tend to"]):
        prob = 0.75
    # Weak cues
    elif any(k in s for k in ["may", "might", "can", "could", "sometimes", "occasionally"]):
        prob = 0.6
    # Negation reduces confidence
    if any(k in s for k in ["not", "no ", "cannot", "none", "rarely", "hardly"]):
        prob -= 0.2
    # Bound within [0.1, 0.99]
    prob = max(0.1, min(prob, 0.99))
    return prob


def parse_raised_levels(effect_str: str) -> Optional[Tuple[str, List[str]]]:
    """Parse patterns like 'raised levels of X, Y and Z' from a string.

    :param effect_str: The substring of text following a causal verb.
    :return: A tuple of (direction, list_of_entities) or None if no match.
    """
    pattern = re.compile(r"\b(raised|elevated|increased|decreased|lowered)\s+levels?\s+of\s+", re.IGNORECASE)
    m = pattern.search(effect_str)
    if not m:
        return None
    direction_word = m.group(1).lower()
    remainder = effect_str[m.end():]
    # Truncate at first sentence boundary (., ; or newline)
    remainder = re.split(r"[\.;]", remainder, maxsplit=1)[0]
    # Split on commas and conjunctions
    items = re.split(r"\s*(?:,|and|&|plus)\s*", remainder)
    entities = []
    for item in items:
        itm = item.strip()
        if not itm:
            continue
        # Remove frequency adverbs
        itm = re.sub(r"^(?:often|sometimes|usually|rarely|frequently)\s+", "", itm, flags=re.IGNORECASE).strip()
        # Remove trailing qualifiers like 'levels', 'level', 'titres', 'titre'
        itm = re.sub(r"\b(levels?|titres?)\b", "", itm, flags=re.IGNORECASE).strip()
        if itm:
            entities.append(itm)
    # Determine direction
    direction_map = {
        'raised': 'positive', 'elevated': 'positive', 'increased': 'positive',
        'decreased': 'negative', 'lowered': 'negative',
    }
    direction = direction_map.get(direction_word, None)
    return direction, entities


def detect_direction_and_magnitude(sentence: str) -> Tuple[Optional[str], Optional[str]]:
    """Detect direction (positive/negative) and magnitude from a phrase.

    Returns a tuple of (direction, magnitude).  Direction is 'positive'
    for increases and 'negative' for decreases.  Magnitude is one of
    {'marked', 'moderate', 'mild', None}.
    """
    s = sentence.lower()
    direction = None
    magnitude = None
    # Direction
    if re.search(r"\b(raise|increas|elevat|promot|stimulat|peaked)\w*", s):
        direction = "positive"
    elif re.search(r"\b(decreas|lower|reduc|suppress|inhibit|drop)\w*", s):
        direction = "negative"
    # Magnitude
    if "marked" in s or "markedly" in s or "considerably" in s or "significantly" in s:
        magnitude = "marked"
    elif "moderate" in s or "moderately" in s:
        magnitude = "moderate"
    elif "mild" in s or "mildly" in s or "slight" in s or "slightly" in s:
        magnitude = "mild"
    return direction, magnitude


def detect_temporal_relation(sentence: str) -> Optional[str]:
    """Identify simple temporal relations (before/after/preceded by/followed by)."""
    s = sentence.lower()
    if "preceded by" in s or "preceeded by" in s:
        return "after"
    if "before" in s or "prior to" in s:
        return "before"
    if "after" in s or "following" in s:
        return "after"
    if "simultaneously" in s or "concurrently" in s or "at the same time" in s:
        return "meets"
    return None


###############################################################################
# LLM Extraction (Optional)
###############################################################################

class LLMExtractor:
    """Optional helper to extract relations using an OpenAI model.

    When an API key is provided, this class will send the input text
    together with a prompt requesting extraction of causal relations
    according to the MRL schema.  The response should be returned as
    structured JSON or MRL lines.  This functionality is optional
    and disabled if openai is unavailable or no API key is configured.
    """

    def __init__(self, model: str = "gpt-4", temperature: float = 0.2, api_key: Optional[str] = None):
        self.model = model
        self.temperature = temperature
        self.api_key = os.getenv("OPENAI_API_KEY")
        self.enabled = openai is not None and api_key is not None
        if self.enabled:
            print("enabled")
            api_key = os.getenv("OPENAI_API_KEY")

    def extract(self, text: str) -> List[Dict[str, str]]:
        """Call the language model to extract relations.

        The expected format of the response is a JSON list of objects with
        fields: subject, predicate, object, qualifiers, probability.  This
        is intentionally generic; the conversion to MRL is handled by
        the caller.  If the model call fails or the extractor is not
        enabled, an empty list is returned.
        """
        if not self.enabled:
            print("not enabled")
            return []
        
        System_prompt_x=r"""
            **Role & scope**

            You are a **biomedical relation-extraction assistant**. Work **only** from the input text. Do **not** add facts, diagnoses, or advice. If nothing is extractable, return an **empty JSON array** `[]`.

            **Task**

            From the provided unstructured medical text, extract relations of these types:

            - **causes** — direct cause–effect (e.g., “A results in B”).
            - **influences** — one entity increases/decreases a property of another (e.g., “Drug X lowers BP”).
            - **is_a** — taxonomy/subtype (e.g., “Y is a subtype of Z”).
            - **has_property** — entity described with an attribute/value (e.g., “CRP markedly elevated”).
            - **is_evidence_for** — an observation supports a diagnosis/hypothesis (e.g., “Elevated ALP suggests biliary obstruction”).
            - **has_temporal_relation** — event ordering using Allen interval names (e.g., “before”, “after”, “overlaps”, “during”, “starts”, “finishes”, “equals”, “meets”, “met_by”, “started_by”, “finished_by”, “overlapped_by”).

            **Output format (strict)**

            Return **only** a JSON array of objects. **No extra text**. Use UTF-8, valid JSON, no trailing commas.

            Each object **must** have:

            - `"subject"` *(string)* — surface form of the subject entity from the text, trimmed.
            - `"predicate"` *(enum)* — one of: `causes`, `influences`, `is_a`, `has_property`, `is_evidence_for`, `has_temporal_relation`.
            - `"object"` *(string)* — surface form of the object entity from the text, trimmed.  
              - For `has_property` when the property applies to the same entity (e.g., “CRP elevated”), set `"object"` **equal to** `"subject"` (self-target) and use `"object_property"`/`qualifiers.value` to carry the attribute.
            - `"object_property"` *(string or null)* — the property being influenced/compared (e.g., `"level"`, `"expression"`, `"size"`, `"dose"`). Use `null` if not applicable.
            - `"qualifiers"` *(object)* — optional details. Allowed keys (omit if unknown):  
              - `direction`: `"positive"` or `"negative"` (e.g., increase/decrease).  
              - `magnitude`: one of `"marked"`, `"moderate"`, `"mild"`.  
              - `value`: a stringified numeric or categorical value, if stated (e.g., `"12 mg"`, `"normal"`).  
              - `unit`: measurement unit if separated (e.g., `"mg/dL"`).  
              - `temporal_relation`: Allen name for `has_temporal_relation`.  
              - `modality`: `"asserted"`, `"suspected"`, or `"negated"`.
              - Any other brief, relevant attributes from the text.
            - `"probability"` *(number)* — confidence in [0.1, 0.99], with **max two decimals**.

            **Normalization & constraints**

            - Keep clinical abbreviations as written (e.g., “ALP”, “ERCP”). Trim whitespace. Preserve case of biomedical entities.
            - Use **one object per atomic relation**. Do not bundle multiple effects in one object.
            - **Cross-sentence relations are allowed** if the link is explicit.
            - **Deduplicate**: if two objects have identical `subject`, `predicate`, `object`, `object_property`, and the same `qualifiers` (after sorting keys), keep the one with the **higher** probability.
            - If a cue is clearly **negated**, still output the relation if it’s linguistically present, set `qualifiers.modality = "negated"`, and reduce probability (see below).
            - For **influences**, use when an **agent affects a target’s property** (e.g., “Steroids increase glucose”). If no external agent is stated (e.g., “CRP elevated”), use **has_property**.
            - For **has_property**, put the **property name** in `"object_property"` (e.g., `"level"`, `"severity"`, `"expression"`) and the **stated value** in `qualifiers.value` (plus `direction`/`magnitude` if applicable).

            **Temporal relations (Allen mapping)**

            Map explicit cues to Allen names; prefer the most specific:
            - “before”, “after”, “during”, “overlaps”, “starts”, “finishes”, “equals”, “meets”, “met by”, “started by”, “finished by”, “overlapped by”.

            **Probability estimation (deterministic)**

            Start at **0.99**, then apply the first applicable cue (or combine if obviously additive). Round to **two decimals** and clamp to [0.10, 0.99].

            - Strong causal cues (always/definitely/certainly/will invariably): **→ 0.95**
            - Direct causation verbs (results in/leads to/causes/induces): **→ 0.90**
            - Moderate cues (usually/often/generally/tends to/suggests): **→ 0.75**
            - Weak cues (may/might/can/could/sometimes/occasionally/associated with): **→ 0.60**
            - Negation/hedge words (not/no/cannot/none/rarely/hardly): **subtract 0.20** from the above tier (min **0.10**).  
              *Example:* “may not” ⇒ 0.60 − 0.20 = **0.40**.
            - If multiple reinforcing strong cues appear, increase by **+0.03** up to **0.95**.

            **Failure behavior**

            - If **no relations** are found, return `[]`.
            - If a required field is missing, **omit the object** entirely (do not emit partial fields).

            **Input**

            You will be given a single key:
            ```json
            {"text": "<raw medical text here>"}
            ```

            **Output JSON Schema (Draft-07)**

            ```json
            {
              "$schema": "http://json-schema.org/draft-07/schema#",
              "type": "array",
              "items": {
                "type": "object",
                "required": ["subject", "predicate", "object", "object_property", "qualifiers", "probability"],
                "properties": {
                  "subject": { "type": "string", "minLength": 1 },
                  "predicate": { "type": "string", "enum": ["causes","influences","is_a","has_property","is_evidence_for","has_temporal_relation"] },
                  "object": { "type": "string", "minLength": 1 },
                  "object_property": { "type": ["string","null"] },
                  "qualifiers": { "type": "object", "additionalProperties": true },
                  "probability": { "type": "number", "minimum": 0.1, "maximum": 0.99 }
                },
                "additionalProperties": false
              }
            }
            ```
            """
        
        prompt2 = (
            r"""You are a biomedical text processing assistant.

                Your task is to analyse the provided unstructured medical text and identify all relevant relations between entities.  Specifically, extract:

                • **Causal relations** – direct cause–effect assertions (e.g. “A results in B”).
                • **Influence relations** – where one entity raises or lowers a property of another (e.g. “X levels are markedly increased”).
                • **Taxonomic relations** – “is_a” hierarchies (e.g. “Y is a subtype of Z”).
                • **Property associations** – when an entity is described as having a property or attribute (e.g. “ALP has a normal bilirubin”).
                • **Evidence relations** – when an observation supports a diagnosis or hypothesis (e.g. “Elevated ALP suggests biliary obstruction”).
                • **Temporal relations** – ordering of events using Allen’s interval names such as “before”, "during", "yet, "still" or “after”.

                For each relation you find, return a JSON object with these keys:

                - `"subject"`: the name of the subject entity (string).
                - `"predicate"`: one of the following strings: `causes`, `influences`, `is_a`, `has_property`, `is_evidence_for`, `has_temporal_relation`.
                - `"object"`: the name of the object entity (string).
                - `"object_property"`: the name of the property being influenced or compared (string) or `null` if not applicable.
                - `"qualifiers"`: a dictionary of qualifiers such as `direction` (`positive`/`negative`), `magnitude` (`marked`, `moderate`, `mild`), `temporal_relation` (e.g. `before`), and any other relevant attributes.
                - `"probability"`: a float between 0 and 1 representing your confidence in the relation.

                ### Estimating probability
                Please reason over the strength of each statement and assign a probability according to these guidelines.  Start from a base of 0.99 and adjust down based on the cues present:

                - **Strong causal cues** (e.g. “always,” “definitely,” “certainly,” “will invariably”) → around 0.95.
                - **Direct causation verbs** (e.g. “results in,” “leads to,” “causes,” “induces”) → around 0.9.
                - **Moderate cues** (e.g. “usually,” “often,” “generally,” “tends to”) → around 0.75.
                - **Weak cues** (e.g. “may,” “might,” “can,” “could,” “sometimes,” “occasionally”) → around 0.6.
                - **Negations or hedge words** (e.g. “not,” “no,” “cannot,” “none,” “rarely,” “hardly”) should reduce the probability by about 0.2.

                Bound all probabilities to the interval [0.1, 0.99].  Use your judgement to refine these estimates based on context; for example, combine multiple cues or adjust for uncertainty.

                Return your results as an array of JSON objects with no extra commentary or explanations.\n\n"""
            f"Text:\n{text.strip()}"
        )        
                        #messages=[{"role": "system", "content": f"""{System_prompt_x}"""}, #"You are a biomedical text processing assistant."},
                                            #{"role": "user", "content": f"""{"text": {text.strip()}}"""}], #prompt2}],
        
        try:
            response = client.chat.completions.create(
                model=self.model,
                messages=[{"role": "system", "content": f"""{System_prompt_x}"""},
                                    {"role": "user", "content": f"""{{"text": {text.strip()}}}"""}],
                temperature=self.temperature,
            )
            #print(response.messages)
            #print(response)
            # Extract the content from the assistant message
            content = response.choices[0].message.content.strip()
            # Attempt to parse JSON
            import json
            relations = json.loads(content)
            return relations
        except Exception:
            # If anything goes wrong (API error, JSON decode error), fall back
            exc_type, exc_value, exc_traceback = sys.exc_info()
            lines = traceback.format_exception(exc_type, exc_value, exc_traceback)
            print (''.join('!! ' + line for line in lines))
            return []


###############################################################################
# Core MRL Generator
###############################################################################

class MRLGenerator:
    """Main class for converting unstructured text into MRL statements."""

    def __init__(self, ontology: Optional[OntologyConnector] = None, use_llm: bool = True, llm_model: str = "gpt-4.1", llm_api_key: Optional[str] = None):
        self.ontology = ontology or OntologyConnector()
        self.entity_id_gen = UniqueIDGenerator(prefix="E")
        self.property_id_gen = UniqueIDGenerator(prefix="P")
        self.entities: Dict[str, Entity] = {}  # map from canonical name to Entity
        self.properties: Dict[str, Entity] = {}  # property name -> Entity
        self.use_llm = use_llm
        self.llm_extractor: Optional[LLMExtractor] = None
        if use_llm:
            llm_api_key = os.getenv("OPENAI_API_KEY")
            self.llm_extractor = LLMExtractor(model=llm_model, api_key=llm_api_key)

    def get_or_create_entity(self, name: str, suggested_type: str = "entity") -> Entity:
        """Retrieve an existing entity or create a new one with a unique identifier."""
        canonical = name.strip()
        if canonical in self.entities:
            return self.entities[canonical]
        # Attempt to resolve URI via ontology connector
        uri = self.ontology.lookup(canonical)
        entity_id = self.entity_id_gen.next_id()
        entity = Entity(name=canonical, entity_type=suggested_type, uri=uri, id=entity_id)
        self.entities[canonical] = entity
        return entity

    def get_or_create_property(self, name: str) -> Entity:
        """Retrieve or create a property entity. Properties share the Entity class."""
        canonical = name.strip()
        if canonical in self.properties:
            return self.properties[canonical]
        # Properties are typed as 'property'
        uri = self.ontology.lookup(canonical)
        prop_id = self.property_id_gen.next_id()
        prop = Entity(name=canonical, entity_type="property", uri=uri, id=prop_id)
        self.properties[canonical] = prop
        return prop

    def extract_relations_heuristic(self, sentence: str, source_id: str) -> List[Relation]:
        """Heuristic extraction of relations from a sentence.

        This method applies a suite of regular expressions to identify
        various relation types.  It does not attempt deep parsing and
        will inevitably miss or misinterpret some statements.  It is
        intended as a baseline and can be improved or replaced.
        """
        relations: List[Relation] = []
        s = sentence.strip()
        lowered = s.lower()

        # 1. Causal relations: X causes/results in/leads to Y
        cause_pattern = re.compile(r"(.+?)\b(?:causes|results in|leads to|induces|triggers)\b(.+)", re.IGNORECASE)
        cause_match = cause_pattern.search(s)
        if cause_match:
            cause_str = cause_match.group(1).strip().rstrip(',')
            effect_str = cause_match.group(2).strip().rstrip('.')
            # Extract entity names (use first entity phrase found in cause_str and effect_str)
            cause_entities = tokenize_entities(cause_str) or [cause_str]
            effect_entities = tokenize_entities(effect_str) or [effect_str]
            # Choose the most specific entity (skip generic measurement words)
            cause_name = self._select_entity_name(cause_entities)
            effect_name = self._select_entity_name(effect_entities)
            cause_entity = self.get_or_create_entity(cause_name, suggested_type="entity")
            effect_entity = self.get_or_create_entity(effect_name, suggested_type="entity")
            prob = compute_probability(s, "causes")
            rel = Relation(subject_id=cause_entity.id, predicate="causes", object_id=effect_entity.id, probability=prob, provenance={"source": source_id})
            relations.append(rel)

            # Additional heuristic: parse patterns like 'raised levels of A, B and C'
            parsed = parse_raised_levels(effect_str)
            if parsed:
                dir_sign, entities_list = parsed
                for item_name in entities_list:
                    ent = self.get_or_create_entity(item_name, suggested_type="entity")
                    prop = self.get_or_create_property("level")
                    qualifiers = {}
                    if dir_sign:
                        qualifiers['direction'] = dir_sign
                    prob_inf = compute_probability(s, "influences")
                    rel_inf = Relation(subject_id=cause_entity.id, predicate="influences", object_id=ent.id,
                                       object_property_id=prop.id, probability=prob_inf, qualifiers=qualifiers,
                                       provenance={"source": source_id})
                    relations.append(rel_inf)

        # 2. Influence relations: X raised/increased/decreased/lowered Y
        # Pattern: entity phrase followed by optional modal verbs and magnitude, then a direction verb
        influence_pattern = re.compile(
            r"(.+?)\b(?:will|often|usually|generally|may|might|can|could|tends to|tend to|tend)\s+"  # modal and filler
            r"(?:be\s+)?"  # optional 'be'
            r"(?:(markedly|moderately|mildly|mild|moderate|marked|significantly|considerably|slightly)\s+)?"  # magnitude
            r"(raised|increased|elevated|decreased|lowered|reduced|suppressed|peaked|fluctuating)\b"
            r"(?:.+?)?"  # allow trailing words
            , re.IGNORECASE)
        # We also consider direct patterns like "X rises" or "X levels are raised"
        simple_influence_pattern = re.compile(
            r"(.+?)\b(levels?|titres?|ratios?)\s*(?:are|is|were|was|have been|has been)?\s*"
            r"(?:(markedly|moderately|mildly|significantly|considerably|slightly)\s+)?"
            r"(raised|increased|elevated|decreased|lowered|reduced|suppressed|fluctuating|peaked)\b"
            , re.IGNORECASE)
        # Search both patterns
        for pat in [influence_pattern, simple_influence_pattern]:
            for match in pat.finditer(s):
                full_match = match.group(0)
                magnitude_word = match.group(1)
                direction_word = match.group(2)
                start_str = match.group(0)
                # Determine the subject (entity being influenced) by taking the first capitalised phrase before the verb
                prefix = s[:match.start()].strip()
                candidates = tokenize_entities(prefix)
                if not candidates:
                    continue
                subject_name = candidates[-1]  # choose the last candidate before verb
                # Determine which property is being influenced (levels, titres, ratio), default to 'level'
                property_name = "level"
                if match.re is simple_influence_pattern:
                    # second group is the property term (levels, titres, ratio)
                    property_term = match.group(2) or "level"
                    property_name = property_term.lower()
                # Create or fetch entities and property
                subj = self.get_or_create_entity(subject_name, suggested_type="entity")
                prop = self.get_or_create_property(property_name)
                # Determine direction and magnitude
                direction = "positive" if direction_word.lower() in ["raised", "increased", "elevated", "peaked", "fluctuating"] else "negative"
                mag_map = {"markedly": "marked", "marked": "marked", "significantly": "marked", "considerably": "marked",
                           "moderately": "moderate", "moderate": "moderate",
                           "mildly": "mild", "mild": "mild", "slightly": "mild"}
                magnitude = mag_map.get((magnitude_word or "").lower()) if magnitude_word else None
                qualifiers = {}
                if direction:
                    qualifiers["direction"] = direction
                if magnitude:
                    qualifiers["magnitude"] = magnitude
                # Probability
                prob = compute_probability(s, "influences")
                # Build relation
                rel = Relation(subject_id=subj.id, predicate="influences", object_id=subj.id, object_property_id=prop.id,
                               probability=prob, qualifiers=qualifiers, provenance={"source": source_id})
                relations.append(rel)

        # 3. Evidential relations: X signifies/suggests/indicates Y
        evidence_pattern = re.compile(r"(.+?)\b(signifies|signify|suggests|suggest|indicates|indicate|points to|point to|implies|imply)\b(.+)", re.IGNORECASE)
        evidence_match = evidence_pattern.search(s)
        if evidence_match:
            evidence_str = evidence_match.group(1).strip()
            hypothesis_str = evidence_match.group(3).strip().rstrip('.')
            evidence_entities = tokenize_entities(evidence_str) or [evidence_str]
            hypothesis_entities = tokenize_entities(hypothesis_str) or [hypothesis_str]
            hyp_name = self._select_entity_name(hypothesis_entities)
            hyp_entity = self.get_or_create_entity(hyp_name, suggested_type="entity")
            prob = compute_probability(s, "is_evidence_for")
            # For each evidence entity, create an is_evidence_for relation
            for ev_cand in evidence_entities:
                ev_name = self._select_entity_name([ev_cand])
                # Skip generic measurement terms
                if ev_name.lower() in {"levels", "level", "enzyme titres", "titres"}:
                    continue
                ev_entity = self.get_or_create_entity(ev_name, suggested_type="finding")
                rel = Relation(subject_id=ev_entity.id, predicate="is_evidence_for", object_id=hyp_entity.id, probability=prob, provenance={"source": source_id})
                relations.append(rel)

        # 4. Taxonomic relations: X is a Y
        isa_pattern = re.compile(r"(\b[A-Z][\w\s\-:]+?)\s+is\s+(?:an?|the)\s+([A-Z][\w\s\-:]+)\b")
        isa_match = isa_pattern.search(s)
        if isa_match:
            sub_str = isa_match.group(1).strip()
            super_str = isa_match.group(2).strip()
            sub_entities = tokenize_entities(sub_str) or [sub_str]
            super_entities = tokenize_entities(super_str) or [super_str]
            sub_entity = self.get_or_create_entity(sub_entities[0], suggested_type="entity")
            super_entity = self.get_or_create_entity(super_entities[0], suggested_type="entity")
            prob = compute_probability(s, "is_a")
            rel = Relation(subject_id=sub_entity.id, predicate="is_a", object_id=super_entity.id, probability=prob, provenance={"source": source_id})
            relations.append(rel)

        # 5. Property associations: X associated with Y
        prop_pattern = re.compile(r"(.+?)\b(associated with|related to|linked to|correlated with)\b(.+)", re.IGNORECASE)
        prop_match = prop_pattern.search(s)
        if prop_match:
            ent_str = prop_match.group(1).strip()
            prop_value = prop_match.group(3).strip().rstrip('.')
            ent_entities = tokenize_entities(ent_str) or [ent_str]
            property_value_entities = tokenize_entities(prop_value) or [prop_value]
            ent_name = self._select_entity_name(ent_entities)
            # Skip spurious property relations if entity is a temporal/conjunction word
            if ent_name.lower() in {"when", "if", "in", "on", "at", "because", "otherwise"}:
                pass  # do nothing
            else:
                ent = self.get_or_create_entity(ent_name, suggested_type="entity")
                # The property value may be a literal string.  Strip leading articles 'a' or 'an'.
                prop_entity_name = property_value_entities[0]
                prop_clean = re.sub(r"^(?:a|an)\s+", "", prop_entity_name, flags=re.IGNORECASE).strip()
                # Create or get property entity
                prop_ent = self.get_or_create_property(prop_clean)
                prob = compute_probability(s, "has_property")
                rel = Relation(subject_id=ent.id, predicate="has_property", object_id=prop_ent.id, probability=prob, provenance={"source": source_id})
                relations.append(rel)

        # 6. Temporal relations: X before/after Y or preceded by Y
        temporal_pattern = re.compile(r"(.+?)\b(before|after|preceded by|followed by)\b(.+)", re.IGNORECASE)
        temporal_match = temporal_pattern.search(s)
        if temporal_match:
            first_str = temporal_match.group(1).strip()
            rel_word = temporal_match.group(2).lower()
            second_str = temporal_match.group(3).strip().rstrip('.')
            first_entities = tokenize_entities(first_str) or [first_str]
            second_entities = tokenize_entities(second_str) or [second_str]
            first_ent = self.get_or_create_entity(first_entities[0], suggested_type="finding")
            second_ent = self.get_or_create_entity(second_entities[0], suggested_type="finding")
            temporal_rel = "before" if rel_word in ["before", "preceded by"] else "after"
            prob = compute_probability(s, "has_temporal_relation")
            rel = Relation(subject_id=first_ent.id, predicate="has_temporal_relation", object_id=second_ent.id, probability=prob, qualifiers={"temporal_relation": temporal_rel}, provenance={"source": source_id})
            relations.append(rel)

        # 7. Comparative property relations within a condition: "When due to X, Y.prop1 compared to Z.prop2"
        cond_pattern = re.compile(r"when\s+(?:due to|caused by)\s+([A-Z][\w\s\-:]+?),\s+(.+)", re.IGNORECASE)
        cond_match = cond_pattern.search(s)
        if cond_match:
            condition_str = cond_match.group(1).strip()
            remainder = cond_match.group(2).strip()
            # Look for "levels of A and B tend to fluctuate" or "X levels are higher than Y"
            # Capture only the entity names (capital words) without trailing verbs
            fluct_pattern = re.compile(
                r"levels?\s+of\s+([A-Z][\w:]+)\s+and\s+([A-Z][\w:]+)\s+(?:tend\s+to\s+)?(fluctuate|fluctuating)",
                re.IGNORECASE,
            )
            comp_pattern = re.compile(
                r"([A-Z][\w:]+)\s+(?:levels?|titres?|ratios?)\s+are\s+(?:higher|greater|lower|less)\s+than\s+([A-Z][\w:]+)",
                re.IGNORECASE,
            )
            m1 = fluct_pattern.search(remainder)
            m2 = comp_pattern.search(remainder)
            cond_entity = self.get_or_create_entity(condition_str, suggested_type="entity")
            if m1:
                ent1 = m1.group(1).strip()
                ent2 = m1.group(2).strip()
                e1 = self.get_or_create_entity(ent1, suggested_type="entity")
                e2 = self.get_or_create_entity(ent2, suggested_type="entity")
                prop_id = self.get_or_create_property("level").id
                qualifiers = {"direction": "positive", "magnitude": "moderate"}  # heuristically assign
                prob = compute_probability(s, "influences")
                rel = Relation(subject_id=cond_entity.id, predicate="condition_influences", object_id=e1.id,
                               object_property_id=prop_id, probability=prob, qualifiers=qualifiers, provenance={"source": source_id})
                relations.append(rel)
                # Also for second entity
                rel2 = Relation(subject_id=cond_entity.id, predicate="condition_influences", object_id=e2.id,
                                object_property_id=prop_id, probability=prob, qualifiers=qualifiers, provenance={"source": source_id})
                relations.append(rel2)
            elif m2:
                ent1 = m2.group(1).strip()
                ent2 = m2.group(2).strip()
                e1 = self.get_or_create_entity(ent1, suggested_type="entity")
                e2 = self.get_or_create_entity(ent2, suggested_type="entity")
                prop1 = self.get_or_create_property("level").id
                prop2 = self.get_or_create_property("level").id
                comparative_relation = "greater" if "higher" in m2.group(0).lower() or "greater" in m2.group(0).lower() else "less"
                prob = compute_probability(s, "has_comparative_property")
                rel = Relation(subject_id=cond_entity.id, predicate="condition_has_comparative_property",
                               object_id=e1.id, comparative_obj_id2=e2.id,
                               comparative_property_id1=prop1, comparative_property_id2=prop2,
                               comparative_relation=comparative_relation, probability=prob,
                               provenance={"source": source_id})
                relations.append(rel)
        return relations

    def convert_llm_relations(self, llm_relations: List[Dict[str, str]], source_id: str) -> List[Relation]:
        """Convert relations extracted by an LLM into Relation objects."""
        relations: List[Relation] = []
        for r in llm_relations:
            subj_name = r.get("subject", "").strip()
            pred = r.get("predicate", "").strip()
            obj_name = r.get("object", "").strip()
            obj_prop_name = r.get("object_property") or None
            qualifiers = r.get("qualifiers") or {}
            probability = float(r.get("probability", 0.7))
            subj_entity = self.get_or_create_entity(subj_name)
            obj_entity = self.get_or_create_entity(obj_name)
            obj_prop_id = None
            if obj_prop_name:
                obj_prop = self.get_or_create_property(obj_prop_name)
                obj_prop_id = obj_prop.id
            relation = Relation(subject_id=subj_entity.id, predicate=pred, object_id=obj_entity.id,
                                object_property_id=obj_prop_id, probability=probability,
                                qualifiers=qualifiers, provenance={"source": source_id})
            relations.append(relation)
        return relations

    def generate_for_document(self, text: str, source_id: str) -> List[Relation]:
        """Generate relations for a single document."""
        relations: List[Relation] = []
        sentences = split_sentences(text)
        for sent in sentences:
            # Use LLM extraction if enabled and we have any non‑empty relations
            llm_relations: List[Dict[str, str]] = []
            if self.use_llm and self.llm_extractor is not None:
                llm_relations = self.llm_extractor.extract(sent)
            if llm_relations:
                relations.extend(self.convert_llm_relations(llm_relations, source_id))
            else:
                relations.extend(self.extract_relations_heuristic(sent, source_id))
        with open('relations.json', 'w', encoding='utf-8') as f:
            f.write(str(relations))
        return relations

    def generate_mrl(self, documents: List[Tuple[str, str]]) -> List[str]:
        """Generate the full set of MRL statements for all documents.

        The return value is a list of lines, including all definition
        statements followed by all relation statements.  Entities and
        properties are defined once, even if they occur in multiple
        documents.
        """
        all_relations: List[Relation] = []
        for text, source_id in documents:
            all_relations.extend(self.generate_for_document(text, source_id))
        # Build definition lines
        lines: List[str] = []
        # Entities definitions
        for entity in self.entities.values():
            uri_str = entity.uri if entity.uri else f"local:{entity.id}"
            lines.append(f"define {entity.entity_type} {entity.id} \"{entity.name}\" \"{uri_str}\"")
        # Property definitions
        for prop in self.properties.values():
            uri_str = prop.uri if prop.uri else f"local:{prop.id}"
            lines.append(f"define property {prop.id} \"{prop.name}\" \"{uri_str}\"")
        # Relation lines
        for rel in all_relations:
            lines.append(rel.to_mrl())
        return lines

    # ---------------------------------------------------------------------
    # Helper: Select an appropriate entity name from a list of candidates.
    # ---------------------------------------------------------------------
    def _select_entity_name(self, candidates: List[str]) -> str:
        """Choose the most specific entity name from a list of candidates.

        The heuristic prefers names that do not belong to a set of generic
        measurement terms (such as "Levels" or "Enzyme titres") and
        chooses the longest remaining candidate.  If all candidates are
        generic, it returns the last candidate in the list.
        """
        generic_terms = {
            'levels', 'level', 'enzyme', 'enzymes', 'enzyme titres', 'titres',
            'ratio', 'ratios', 'ast:alt ratio', 'ast', 'alt', 'ast:alt',
            'when', 'the', 'this', 'these', 'that', 'it', 'they',
            'i/u', 'iu', 'u/i'
        }
        # Preprocess candidates: strip leading articles and whitespace
        cleaned_candidates: List[str] = []
        for c in candidates:
            c_clean = re.sub(r"^(?:a|an|the)\s+", "", c, flags=re.IGNORECASE).strip()
            cleaned_candidates.append(c_clean)
        # Remove generic measurement terms
        filtered = [c for c in cleaned_candidates if c.lower() not in generic_terms]
        if not filtered:
            return candidates[-1]
        # Prefer the longest (most specific) candidate
        return max(filtered, key=len)


###############################################################################
# Public API
###############################################################################

def generate_mrl(documents: List[Tuple[str, str]], use_llm: bool = True, llm_model: str = "gpt-4.1", llm_api_key: Optional[str] = None) -> List[str]:
    """Generate MRL lines from a list of (text, source_id) documents.

    :param documents: List of tuples containing the unstructured text and a
                       source identifier (e.g., PMID, DOI, or file name)
    :param use_llm: If True and an API key is provided, attempt to use
                    a language model to extract relations.  When False
                    (default), rely solely on heuristic extraction.
    :param llm_model: Name of the OpenAI model to use.  Ignored if
                      ``use_llm`` is False.
    :param llm_api_key: API key for OpenAI.  Must be provided if
                        ``use_llm`` is True.  Ignored otherwise.
    :return: List of MRL statements as strings.
    """
    generator = MRLGenerator(use_llm=use_llm, llm_model=llm_model, llm_api_key=llm_api_key)
    return generator.generate_mrl(documents)


if __name__ == "__main__":
    import argparse
    parser = argparse.ArgumentParser(description="Generate MRL from unstructured medical text.")
    parser.add_argument("--input", type=str, nargs="*", help="Paths to text files containing medical documents.")
    parser.add_argument("--source", type=str, nargs="*", help="Source identifiers corresponding to each input file.")
    parser.add_argument("--use-llm", action="store_true", help="Enable LLM extraction (requires OpenAI API key).")
    parser.add_argument("--llm-model", type=str, default="gpt-4.1", help="OpenAI model name (default: gpt-4.1).")
    parser.add_argument("--llm-api-key", type=str, default=None, help="OpenAI API key.")
    parser.add_argument("--output", type=str, default=None, help="Output file to write the MRL statements.")
    args = parser.parse_args()
    # Read documents from files
    docs: List[Tuple[str, str]] = []
    if args.input:
        if not args.source or len(args.source) != len(args.input):
            raise ValueError("The number of sources must match the number of input files.")
        for path, src in zip(args.input, args.source):
            with open(path, "r", encoding="utf-8") as f:
                docs.append((f.read(), src))
    else:
        # If no input files are provided, read from stdin
        text = input("Enter text: ")
        docs.append((text, "user_input"))
    lines = generate_mrl(docs, use_llm=args.use_llm, llm_model=args.llm_model, llm_api_key=args.llm_api_key)
    if args.output:
        with open(args.output, "w", encoding="utf-8") as f:
            for line in lines:
                f.write(line + "\n")
    else:
        for line in lines:
            print(line)