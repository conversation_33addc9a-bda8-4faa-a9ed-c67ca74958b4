[Relation(subject_id='E1', predicate='causes', object_id='E2', object_property_id=None, probability=0.9, qualifiers={}, provenance={'source': 'input_text:1'}, comparative_obj_id2=None, comparative_property_id1=None, comparative_property_id2=None, comparative_relation=None), Relation(subject_id='E1', predicate='influences', object_id='E2', object_property_id='P1', probability=0.9, qualifiers={'direction': 'positive'}, provenance={'source': 'input_text:1'}, comparative_obj_id2=None, comparative_property_id1=None, comparative_property_id2=None, comparative_relation=None), Relation(subject_id='E1', predicate='influences', object_id='E3', object_property_id='P1', probability=0.9, qualifiers={'direction': 'positive'}, provenance={'source': 'input_text:1'}, comparative_obj_id2=None, comparative_property_id1=None, comparative_property_id2=None, comparative_relation=None), Relation(subject_id='E1', predicate='influences', object_id='E4', object_property_id='P1', probability=0.9, qualifiers={'direction': 'positive'}, provenance={'source': 'input_text:1'}, comparative_obj_id2=None, comparative_property_id1=None, comparative_property_id2=None, comparative_relation=None), Relation(subject_id='E2', predicate='is_evidence_for', object_id='E5', object_property_id=None, probability=0.5, qualifiers={}, provenance={'source': 'input_text:3'}, comparative_obj_id2=None, comparative_property_id1=None, comparative_property_id2=None, comparative_relation=None), Relation(subject_id='E3', predicate='is_evidence_for', object_id='E5', object_property_id=None, probability=0.5, qualifiers={}, provenance={'source': 'input_text:3'}, comparative_obj_id2=None, comparative_property_id1=None, comparative_property_id2=None, comparative_relation=None), Relation(subject_id='E6', predicate='causes', object_id='E7', object_property_id=None, probability=0.9, qualifiers={}, provenance={'source': 'input_text:4'}, comparative_obj_id2=None, comparative_property_id1=None, comparative_property_id2=None, comparative_relation=None), Relation(subject_id='E2', predicate='has_property', object_id='P2', object_property_id=None, probability=0.75, qualifiers={}, provenance={'source': 'input_text:5'}, comparative_obj_id2=None, comparative_property_id1=None, comparative_property_id2=None, comparative_relation=None), Relation(subject_id='E8', predicate='condition_influences', object_id='E2', object_property_id='P1', probability=0.75, qualifiers={'direction': 'positive', 'magnitude': 'moderate'}, provenance={'source': 'input_text:5'}, comparative_obj_id2=None, comparative_property_id1=None, comparative_property_id2=None, comparative_relation=None), Relation(subject_id='E8', predicate='condition_influences', object_id='E3', object_property_id='P1', probability=0.75, qualifiers={'direction': 'positive', 'magnitude': 'moderate'}, provenance={'source': 'input_text:5'}, comparative_obj_id2=None, comparative_property_id1=None, comparative_property_id2=None, comparative_relation=None), Relation(subject_id='E9', predicate='has_temporal_relation', object_id='E10', object_property_id=None, probability=0.75, qualifiers={'temporal_relation': 'before'}, provenance={'source': 'input_text:6'}, comparative_obj_id2=None, comparative_property_id1=None, comparative_property_id2=None, comparative_relation=None), Relation(subject_id='E12', predicate='is_evidence_for', object_id='E11', object_property_id=None, probability=0.5, qualifiers={}, provenance={'source': 'input_text:8'}, comparative_obj_id2=None, comparative_property_id1=None, comparative_property_id2=None, comparative_relation=None), Relation(subject_id='E12', predicate='is_evidence_for', object_id='E13', object_property_id=None, probability=0.5, qualifiers={}, provenance={'source': 'input_text:10'}, comparative_obj_id2=None, comparative_property_id1=None, comparative_property_id2=None, comparative_relation=None)]