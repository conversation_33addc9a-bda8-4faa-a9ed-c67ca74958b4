[Relation(subject_id='E1', predicate='causes', object_id='E2', object_property_id=None, probability=0.9, qualifiers={}, provenance={'source': '1'}, comparative_obj_id2=None, comparative_property_id1=None, comparative_property_id2=None, comparative_relation=None), Relation(subject_id='E1', predicate='causes', object_id='E3', object_property_id=None, probability=0.9, qualifiers={}, provenance={'source': '1'}, comparative_obj_id2=None, comparative_property_id1=None, comparative_property_id2=None, comparative_relation=None), Relation(subject_id='E1', predicate='causes', object_id='E4', object_property_id=None, probability=0.75, qualifiers={'modality': 'asserted', 'frequency': 'often'}, provenance={'source': '1'}, comparative_obj_id2=None, comparative_property_id1=None, comparative_property_id2=None, comparative_relation=None), Relation(subject_id='E5', predicate='has_property', object_id='E5', object_property_id='P1', probability=0.75, qualifiers={'direction': 'positive', 'magnitude': 'marked', 'value': 'raised', 'modality': 'asserted'}, provenance={'source': '1'}, comparative_obj_id2=None, comparative_property_id1=None, comparative_property_id2=None, comparative_relation=None), Relation(subject_id='E5', predicate='has_property', object_id='E5', object_property_id='P1', probability=0.99, qualifiers={'direction': 'positive', 'value': 'elevated'}, provenance={'source': '1'}, comparative_obj_id2=None, comparative_property_id1=None, comparative_property_id2=None, comparative_relation=None), Relation(subject_id='E6', predicate='has_property', object_id='E6', object_property_id='P1', probability=0.99, qualifiers={'direction': 'positive', 'value': 'elevated'}, provenance={'source': '1'}, comparative_obj_id2=None, comparative_property_id1=None, comparative_property_id2=None, comparative_relation=None), Relation(subject_id='E7', predicate='is_evidence_for', object_id='E8', object_property_id=None, probability=0.9, qualifiers={}, provenance={'source': '1'}, comparative_obj_id2=None, comparative_property_id1=None, comparative_property_id2=None, comparative_relation=None), Relation(subject_id='E9', predicate='causes', object_id='E10', object_property_id=None, probability=0.9, qualifiers={}, provenance={'source': '1'}, comparative_obj_id2=None, comparative_property_id1=None, comparative_property_id2=None, comparative_relation=None), Relation(subject_id='E11', predicate='influences', object_id='E5', object_property_id='P1', probability=0.75, qualifiers={'direction': 'positive', 'modality': 'asserted'}, provenance={'source': '1'}, comparative_obj_id2=None, comparative_property_id1=None, comparative_property_id2=None, comparative_relation=None), Relation(subject_id='E11', predicate='influences', object_id='E6', object_property_id='P1', probability=0.75, qualifiers={'direction': 'positive', 'modality': 'asserted'}, provenance={'source': '1'}, comparative_obj_id2=None, comparative_property_id1=None, comparative_property_id2=None, comparative_relation=None), Relation(subject_id='E5', predicate='has_property', object_id='E5', object_property_id='P1', probability=0.75, qualifiers={'value': 'fluctuate', 'modality': 'asserted'}, provenance={'source': '1'}, comparative_obj_id2=None, comparative_property_id1=None, comparative_property_id2=None, comparative_relation=None), Relation(subject_id='E6', predicate='has_property', object_id='E6', object_property_id='P1', probability=0.75, qualifiers={'value': 'fluctuate', 'modality': 'asserted'}, provenance={'source': '1'}, comparative_obj_id2=None, comparative_property_id1=None, comparative_property_id2=None, comparative_relation=None), Relation(subject_id='E11', predicate='is_evidence_for', object_id='E12', object_property_id=None, probability=0.6, qualifiers={'modality': 'asserted', 'association': 'may be associated'}, provenance={'source': '1'}, comparative_obj_id2=None, comparative_property_id1=None, comparative_property_id2=None, comparative_relation=None), Relation(subject_id='E13', predicate='has_property', object_id='E13', object_property_id='P1', probability=0.75, qualifiers={'direction': 'positive', 'modality': 'asserted'}, provenance={'source': '1'}, comparative_obj_id2=None, comparative_property_id1=None, comparative_property_id2=None, comparative_relation=None), Relation(subject_id='E13', predicate='has_property', object_id='E13', object_property_id='P1', probability=0.75, qualifiers={'direction': 'negative', 'modality': 'asserted'}, provenance={'source': '1'}, comparative_obj_id2=None, comparative_property_id1=None, comparative_property_id2=None, comparative_relation=None), Relation(subject_id='E14', predicate='has_property', object_id='E14', object_property_id='P1', probability=0.99, qualifiers={'value': '>1000', 'unit': 'I/U', 'direction': 'positive', 'modality': 'asserted'}, provenance={'source': '1'}, comparative_obj_id2=None, comparative_property_id1=None, comparative_property_id2=None, comparative_relation=None), Relation(subject_id='E14', predicate='has_temporal_relation', object_id='E15', object_property_id=None, probability=0.99, qualifiers={'temporal_relation': 'before'}, provenance={'source': '1'}, comparative_obj_id2=None, comparative_property_id1=None, comparative_property_id2=None, comparative_relation=None), Relation(subject_id='E16', predicate='is_evidence_for', object_id='E17', object_property_id=None, probability=0.6, qualifiers={'modality': 'suspected'}, provenance={'source': '1'}, comparative_obj_id2=None, comparative_property_id1=None, comparative_property_id2=None, comparative_relation=None), Relation(subject_id='E18', predicate='is_evidence_for', object_id='E19', object_property_id=None, probability=0.6, qualifiers={'modality': 'asserted'}, provenance={'source': '1'}, comparative_obj_id2=None, comparative_property_id1=None, comparative_property_id2=None, comparative_relation=None), Relation(subject_id='E20', predicate='has_property', object_id='E20', object_property_id='P1', probability=0.75, qualifiers={'direction': 'positive', 'magnitude': 'marked', 'value': 'higher', 'comparison': 'AST'}, provenance={'source': '1'}, comparative_obj_id2=None, comparative_property_id1=None, comparative_property_id2=None, comparative_relation=None), Relation(subject_id='E21', predicate='is_evidence_for', object_id='E22', object_property_id=None, probability=0.75, qualifiers={'direction': 'positive', 'value': '>1.5'}, provenance={'source': '1'}, comparative_obj_id2=None, comparative_property_id1=None, comparative_property_id2=None, comparative_relation=None), Relation(subject_id='E23', predicate='has_property', object_id='E23', object_property_id='P2', probability=0.75, qualifiers={'value': 'preferential rise in ALP, rather than GGT, or with an ALT:ALP ratio of <2.', 'modality': 'asserted'}, provenance={'source': '1'}, comparative_obj_id2=None, comparative_property_id1=None, comparative_property_id2=None, comparative_relation=None), Relation(subject_id='E24', predicate='is_a', object_id='E25', object_property_id=None, probability=0.6, qualifiers={}, provenance={'source': '1'}, comparative_obj_id2=None, comparative_property_id1=None, comparative_property_id2=None, comparative_relation=None), Relation(subject_id='E26', predicate='is_a', object_id='E25', object_property_id=None, probability=0.6, qualifiers={}, provenance={'source': '1'}, comparative_obj_id2=None, comparative_property_id1=None, comparative_property_id2=None, comparative_relation=None), Relation(subject_id='E27', predicate='is_a', object_id='E25', object_property_id=None, probability=0.6, qualifiers={}, provenance={'source': '1'}, comparative_obj_id2=None, comparative_property_id1=None, comparative_property_id2=None, comparative_relation=None), Relation(subject_id='E28', predicate='is_a', object_id='E25', object_property_id=None, probability=0.6, qualifiers={}, provenance={'source': '1'}, comparative_obj_id2=None, comparative_property_id1=None, comparative_property_id2=None, comparative_relation=None)]