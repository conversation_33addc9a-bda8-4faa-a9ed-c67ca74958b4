# Entity and Property Definitions

define entity E1 "Mechanical biliary obstruction" "local:E1"
define entity E2 "raised levels of ALP" "local:E2"
define entity E3 "ALP" "local:E3"
define entity E4 "Elevated ALP" "local:E4"
define entity E5 "biliary obstruction" "local:E5"
define entity E6 "fluctuation of ALP levels" "local:E6"
define entity E7 "fluctuation of GGT levels" "local:E7"
define entity E8 "Hepatocellular damage" "local:E8"
define entity E9 "increased levels of ALT" "local:E9"
define entity E10 "increased levels of AST" "local:E10"
define entity E11 "ALT" "local:E11"
define entity E12 "High ALT levels" "local:E12"
define entity E13 "hepatocyte injury" "local:E13"
define entity E14 "Chronic liver disease" "local:E14"
define entity E15 "bilirubin" "local:E15"
define entity E16 "Bilirubin elevation" "local:E16"
define entity E17 "impaired liver function" "local:E17"
define entity E18 "chronic liver disease" "local:E18"
define entity E19 "Portal hypertension" "local:E19"
define entity E20 "Myocardial infarction" "local:E20"
define entity E21 "elevated troponin levels" "local:E21"
define entity E22 "Troponin elevation" "local:E22"
define entity E23 "cardiac muscle damage" "local:E23"
define entity E24 "Chest pain" "local:E24"
define entity E25 "myocardial infarction" "local:E25"
define property P1 "level" "local:P1"
define property P2 "specificity for liver damage" "local:P2"

# Relations grouped by source:chunk_id

## Source: test_cardiology:1
## Original text: "Myocardial infarction causes elevated troponin levels."

0.900:: E20 causes E21 {source:"test_cardiology:1"}

## Source: test_cardiology:2
## Original text: "Troponin elevation indicates cardiac muscle damage."

0.900:: E22 is_evidence_for E23 {source:"test_cardiology:2"}

## Source: test_cardiology:3
## Original text: "Chest pain suggests myocardial infarction."

0.750:: E24 is_evidence_for E25 {source:"test_cardiology:3"}

## Source: test_medical_text:1
## Original text: "Mechanical biliary obstruction results in raised levels of ALP."

0.900:: E1 causes E2 {source:"test_medical_text:1"}
0.990:: E3 has_property E3 [direction=positive,value=raised] {source:"test_medical_text:1"}

## Source: test_medical_text:10
## Original text: "Portal hypertension results from chronic liver disease."

0.900:: E18 causes E19 {source:"test_medical_text:10"}

## Source: test_medical_text:2
## Original text: "ALP will usually be markedly raised in comparison with ALT."

0.750:: E3 has_property E3 [magnitude=marked,direction=positive,modality=asserted] {source:"test_medical_text:2"}

## Source: test_medical_text:3
## Original text: "Elevated ALP suggests biliary obstruction."

0.750:: E4 is_evidence_for E5 {source:"test_medical_text:3"}
0.990:: E3 has_property E3 [direction=positive,value=elevated] {source:"test_medical_text:3"}

## Source: test_medical_text:4
## Original text: "When due to biliary obstruction, levels of ALP and GGT tend to fluctuate."

0.750:: E5 causes E6 {source:"test_medical_text:4"}
0.750:: E5 causes E7 {source:"test_medical_text:4"}

## Source: test_medical_text:5
## Original text: "Hepatocellular damage causes increased levels of ALT and AST."

0.900:: E8 causes E9 {source:"test_medical_text:5"}
0.900:: E8 causes E10 {source:"test_medical_text:5"}

## Source: test_medical_text:6
## Original text: "ALT is more specific for liver damage than AST."

0.990:: E11 has_property E11 [value=more specific,comparison=than AST] {source:"test_medical_text:6"}

## Source: test_medical_text:7
## Original text: "High ALT levels indicate hepatocyte injury."

0.750:: E12 is_evidence_for E13 {source:"test_medical_text:7"}
0.990:: E11 has_property E11 [direction=positive,value=high] {source:"test_medical_text:7"}

## Source: test_medical_text:8
## Original text: "Chronic liver disease is associated with elevated bilirubin levels."

0.600:: E14 influences E15.P1 [direction=positive,value=elevated] {source:"test_medical_text:8"}

## Source: test_medical_text:9
## Original text: "Bilirubin elevation signifies impaired liver function."

0.750:: E16 is_evidence_for E17 {source:"test_medical_text:9"}

