#!/usr/bin/env python3
"""
mrl_bnf_generator.py
====================

This module supports two complementary tasks related to the extraction of
causal knowledge from biomedical text.

1. **MRL grammar generation (legacy)** – When invoked via its original
   entry point (`generate_bnf`), the script tokenises sentences and produces
   a Backus–Naur Form (BNF) representation for a simple causal language.  It
   identifies cause–effect patterns using basic connectors ("results in",
   "suggests", etc.), assigns rough probabilities based on hedging words like
   "usually" or "may", and annotates temporal/negation/uncertainty modifiers.
   This functionality remains for backward compatibility.

2. **MRL concrete syntax generation** – In light of the detailed
   specification for the Medical Causal‑Knowledge Representation Language
   (MRL) described in Part II of the accompanying document, the script also
   provides `generate_mrl`, a high‑level translator that converts annotated
   medical prose into fully formed MRL statements.  These statements use
   the concrete syntax defined in Chapter 5 and adhere to the semantic model
   outlined in Chapter 4.  The translator defines entities (with URIs when
   available), constructs relational assertions (e.g. `influences`,
   `has_property`, `is_evidence_for`), annotates direction, magnitude,
   modality, likelihood and strength qualifiers as required, supports an
   optional probability prefix computed from linguistic cues, and appends
   provenance blocks containing at least a source identifier and reference
   【527310129079257†L226-L241】【36635478994315†L30-L44】.

The script therefore serves as a bridge between unstructured clinical text and
the structured knowledge representation required by a causal CDSS.  When
called from the command line without arguments it reads stdin and emits BNF
rules (legacy mode).  When called with the `--mrl` flag it emits MRL
statements suitable for downstream ingestion.
"""

import re
import sys
import textwrap
from typing import List, Tuple, Dict, Optional, Iterable

###############################################################################
# MRL Grammar (Backus–Naur Form)
###############################################################################

# The following list encodes the complete BNF grammar for the Medical
# Causal‑Knowledge Representation Language (MRL) as defined in Part II of
# the specification.  The grammar is rendered as a sequence of strings so
# that it can easily be emitted from the `generate_bnf` function.  It covers
# the top‑level statement structure, entity and finding definitions,
# relational assertions, qualifier and provenance blocks, and the primitive
# lexical elements.  Comments (lines starting with `//`) mirror those in
# the document for readability.

'''
MRL_GRAMMAR_LINES: List[str] = [
    "// Top-level MRL grammar",
    "<mrl_file>          ::= (<mrl_statement> '\n')*",
    "<mrl_statement>     ::= <definition_statement> | <relation_statement>",
    "",
    "// Entity and Finding Definitions",
    "<definition_statement> ::= \"define\" <entity_type> <id> <string> \"<\" <uri> \">\"",
    "<entity_type>       ::= \"entity\" | \"finding\" | \"property\"",
    "",
    "// Relational Statements",
    "<relation_statement> ::= <prob_prefix>? <assertion> <qualifier_block>? <provenance_block>",
    "<prob_prefix>       ::= <float> \"::\"",
    "<assertion>         ::= <id> \"causes\" <id>",
    "                     | <id> \"influences\" <id> \".\" <id>",
    "                     | <id> \"is_a\" <id>",
    "                     | <id> \"has_property\" (<id> | <string>)",
    "                     | <id> \"is_evidence_for\" <id>",
    "                     | <id> \"has_temporal_relation\" <id> <string>",
    "                     | \"condition\" \"(\" <id> \" )\" \"influences\" <id> \".\" <id>",
    "                     | \"condition\" \"(\" <id> \" )\" \"has_comparative_property\" \"(\" <id> \".\" <id> \"," <id> \".\" <id> \"," <string> \" )\"",
    "",
    "// Reusable Blocks",
    "<qualifier_block>   ::= \"[\" <qualifier> (\",\" <qualifier>)* \"]\"",
    "<qualifier>         ::= <key> \"=\" (<string> | <float>)",
    "<provenance_block>  ::= \"{\" \"source:\" <source_id> (\",\" <prov_field>)* \"}\"",
    "<prov_field>        ::= \"ref:\" <string> | \"agent:\" <string> | \"timestamp:\" <string> | \"confidence:\" <float>",
    "",
    "// Primitives",
    "<id>                ::= [a-zA-Z_][a-zA-Z0-9_]*",
    "<uri>               ::= <string_literal> // A valid URI",
    "<source_id>         ::= <string_literal> // e.g., \"PMID:12345\"",
    "<string>            ::= <string_literal>",
    "<float>             ::= [0-9]+(\".\"[0-9]+)?",
    "<key>               ::= \"direction\" | \"magnitude\" | \"modality\" | \"strength\" | \"likelihood\"",
    "",
    "// Helper for string literals",
    "<string_literal>    ::= '"' (~'"')* '"'",
]
'''
# Override the grammar lines with the full BNF specification.  This list
# mirrors the grammar presented in Part II of the specification.  Each
# element corresponds to a single line of the grammar.  Quotes and
# parentheses are escaped to allow inclusion within Python strings.
MRL_GRAMMAR_LINES: List[str] = [
    "// Top-level statement",
    "<mrl_file>          ::= (<mrl_statement> '\\n')*",
    "<mrl_statement>     ::= <definition_statement> | <relation_statement>",
    "",
    "// Entity and Finding Definitions",
    "<definition_statement> ::= \"define\" <entity_type> <id> <string> \"<\" <uri> \">\"",
    "<entity_type>       ::= \"entity\" | \"finding\" | \"property\"",
    "",
    "// Relational Statements",
    "<relation_statement> ::= <prob_prefix>? <assertion> <qualifier_block>? <provenance_block>",
    "<prob_prefix>       ::= <float> \"::\"",
    "<assertion>         ::= <id> \"causes\" <id>"
    " | <id> \"influences\" <id> \".\" <id>"
    " | <id> \"is_a\" <id>"
    " | <id> \"has_property\" (<id> | <string>)"
    " | <id> \"is_evidence_for\" <id>"
    " | <id> \"has_temporal_relation\" <id> <string>"
    " | \"condition\" \"(\" <id> \" )\" \"influences\" <id> \".\" <id>"
    " | \"condition\" \"(\" <id> \" )\" \"has_comparative_property\" \"(\" <id> \".\" <id> , <id> \".\" <id> , <string> \" )\"",
    "",
    "// Reusable Blocks",
    "<qualifier_block>   ::= \"[\" <qualifier> (\",\" <qualifier>)* \"]\"",
    "<qualifier>         ::= <key> \"=\" ( <string> | <float> )",
    "<provenance_block>  ::= \"{\" \"source:\" <source_id> (\",\" <prov_field>)* \"}\"",
    "<prov_field>        ::= \"ref:\" <string> | \"agent:\" <string> | \"timestamp:\" <string> | \"confidence:\" <float>",
    "",
    "// Primitives",
    "<id>                ::= [a-zA-Z_][a-zA-Z0-9_]*",
    "<uri>               ::= <string_literal> // A valid URI",
    "<source_id>         ::= <string_literal> // e.g., \"PMID:12345\"",
    "<string>            ::= <string_literal>",
    "<float>             ::= [0-9]+(\".\"[0-9]+)?",
    "<key>               ::= \"direction\" | \"magnitude\" | \"modality\" | \"strength\" | \"likelihood\"",
    "",
    "// Helper for string literals",
    "<string_literal>    ::= '\"' (~'\"')* '\"'",
]


def load_probability_mapping() -> Dict[str, float]:
    """Return a mapping from common adverbs/verbs of frequency to probability.

    The values are approximate frequencies derived from pedagogical resources
    describing adverbs of frequency【36635478994315†L30-L44】.  Additional
    modal verbs and discourse markers are assigned reasonable default values
    reflecting their relative certainty.  These mappings may be refined or
    expanded as needed.
    """
    return {
        # Adverbs of frequency – approximate values【36635478994315†L30-L44】
        "always": 1.0,
        "usually": 0.9,
        "normally": 0.8,
        "generally": 0.8,
        "often": 0.7,
        "frequently": 0.7,
        "sometimes": 0.5,
        "occasionally": 0.3,
        "seldom": 0.1,
        "hardly ever": 0.05,
        "rarely": 0.05,
        "never": 0.0,
        # Additional uncertainty/modal verbs
        "may": 0.5,
        "might": 0.5,
        "can": 0.6,
        "could": 0.6,
        # Use only the infinitive + to forms for modal expressions to avoid
        # multiplying duplicate variants (e.g. "tend" vs "tends").  Plural
        # or singular inflections are deliberately omitted because word‑boundary
        # matching may pick them up as substrings of longer words.  Only
        # retain the forms most commonly used in biomedical texts.
        "tend to": 0.6,
        "tends to": 0.6,
        "suggests": 0.5,
        "indicates": 0.6,
        "signify": 0.8,
        "signifies": 0.8,
        "assist": 0.4,
        "assists": 0.4,
        "associated with": 0.5,
        "more likely": 0.7,
        "less likely": 0.3,
    }


def load_connector_mapping() -> Dict[str, float]:
    """Return a mapping from causal connectors to baseline probabilities.

    Connectors are phrases that explicitly convey a causal or indicative
    relationship between two propositions.  Baseline probabilities reflect
    the strength of the linguistic cue on its own.  A value of 1.0
    corresponds to a high degree of certainty, whereas lower values indicate
    weaker implications.
    """
    # When adding new connectors to this mapping, be careful to avoid
    # ambiguous words such as "cause" or "causes" that also occur as
    # nouns in general prose.  Connectors should be phrases that reliably
    # convey directional or indicative relationships.
    return {
        # Common causal connectors
        "causes": 0.9,
        "cause": 0.9,
        "causing": 0.9,
        "results in": 0.95,
        "leads to": 0.95,
        "signify": 0.8,
        "signifies": 0.8,
        "indicates": 0.7,
        "indicate": 0.7,
        "suggests": 0.5,
        "suggest": 0.5,
        "may be associated with": 0.5,
        # Note: the bare phrase "associated with" often appears in
        # descriptive contexts (e.g. "when associated with") and is
        # therefore omitted to avoid spurious matches.  The form "may be
        # associated with" is retained as it clearly expresses a causal
        # association.
        "may assist in": 0.4,
        # "assist in" requires a preceding modal verb (e.g. may) to
        # convey uncertainty.  The bare form is excluded.
        "tend to": 0.6,
        "tends to": 0.6,
        "due to": 0.9,
    }


def split_sentences(text: str) -> List[str]:
    """Split a block of text into sentences.

    A simple heuristic is used: the text is split on periods, question marks
    and exclamation marks followed by whitespace or the end of the string.
    Abbreviations are not handled explicitly but the approach suffices for
    typical medical sentences.  Empty sentences are filtered out.
    """
    # Replace newlines with spaces
    text = text.replace("\n", " ")
    # Attempt to remove simple footnote markers (numbers surrounded by
    # spaces or enclosed in parentheses) without stripping meaningful
    # numeric information like laboratory values (e.g. 1000, 1.5).  This
    # pattern removes isolated numbers with optional punctuation when
    # they are surrounded by whitespace or brackets.  Decimal numbers
    # remain untouched.
    text = re.sub(r"\s*\(\d+\)\s*", " ", text)  # remove (1), (2) etc.
    text = re.sub(r"\s+\[\d+\]\s+", " ", text)  # remove [1], [2] etc.
    # Remove standalone footnote numbers followed by a space but keep decimals
    text = re.sub(r"(?<=\s)\d+(?=\s)", "", text)
    # Split on sentence terminators
    parts = re.split(r"(?<=[.!?])\s+", text)
    return [p.strip() for p in parts if p.strip()]


def detect_qualifiers(sentence: str) -> Dict[str, List[str]]:
    """Detect contextual qualifiers in a sentence.

    Returns a dictionary with lists of qualifiers keyed by type: 'temporal',
    'negation', and 'uncertainty'.  Qualifiers are lower‑cased but the
    original words are preserved in the output for readability.
    """
    temporal_keywords = [
        "gradually",
        "preceded",
        "fluctuate",
        "fluctuates",
        "fluctuating",
        "rise and fall",
        "peak",
        "peaked",
        "rise",
        "fall",
        "tend to fluctuate",
    ]
    negation_keywords = [
        "no",
        "not",
        "normal",
        "absence",
        "lack",
        "without",
    ]
    uncertainty_keywords = [
        "may",
        "might",
        "can",
        "could",
        "tend",
        "tends",
        "tend to",
        "tends to",
        "suggest",
        "suggests",
        "indicate",
        "indicates",
        "likely",
        "more likely",
        "less likely",
        "assist",
        "assists",
        "appear",
        "appears",
        "seem",
        "seems",
    ]

    quals: Dict[str, List[str]] = {"temporal": [], "negation": [], "uncertainty": []}
    lower = sentence.lower()
    for kw in temporal_keywords:
        if kw in lower:
            quals["temporal"].append(kw)
    for kw in negation_keywords:
        # exact match ensures we don't match 'normal' inside 'abnormal'
        pattern = r"\b" + re.escape(kw) + r"\b"
        if re.search(pattern, lower):
            quals["negation"].append(kw)
    for kw in uncertainty_keywords:
        if kw in lower:
            quals["uncertainty"].append(kw)
    return {k: v for k, v in quals.items() if v}


def extract_relations_from_sentence(
    sentence: str,
    connector_map: Dict[str, float],
    probability_map: Dict[str, float],
    doc_id: str,
    sentence_index: int,
) -> List[Dict[str, object]]:
    """Extract causal relations from a single sentence.

    Returns a list of dictionaries, each containing:
        cause (str): the antecedent phrase
        effects (list[str]): list of consequent phrases
        probability (float): combined probability score
        qualifiers (dict): contextual qualifiers
        source (str): document and sentence identifier

    The function identifies the earliest connector in the sentence and splits
    the sentence into cause and effect based on that connector.  For
    sentences beginning with "due to", the cause follows the phrase.
    """
    relations = []
    original_sentence = sentence.strip()
    lower_sentence = original_sentence.lower()
    source_id = f"{doc_id}:{sentence_index}"
    # Check for explicit "due to" constructions first
    due_match = re.search(r"due to\s+([^,]+)", lower_sentence)
    if due_match:
        cause = due_match.group(1).strip()
        # The rest of the sentence after the comma is the effect part
        # Attempt to find the first comma after "due to"
        comma_index = lower_sentence.find(',', due_match.end())
        if comma_index != -1:
            effect_part = original_sentence[comma_index + 1 :].strip()
        else:
            # No comma; effect is remainder of sentence
            effect_part = original_sentence[due_match.end() :].strip()
        effects = split_effects(effect_part)
        qualifiers = detect_qualifiers(original_sentence)
        # Base probability for 'due to'
        prob_list = [connector_map["due to"]]
        # Add probabilities for adverbs/modals found in sentence
        for key, val in probability_map.items():
            if key in lower_sentence:
                prob_list.append(val)
        probability = 1.0
        for p in prob_list:
            probability *= p
        relation = {
            "cause": cause,
            "effects": effects,
            "probability": round(probability, 3),
            "qualifiers": qualifiers,
            "source": source_id,
        }
        relations.append(relation)
        return relations

    # Look for connectors in order of appearance
    found_connector: Optional[Tuple[str, int]] = None
    # Iterate over connectors sorted by length descending to avoid partial
    # matches (e.g. "suggest" inside "suggests").  If two connectors start at
    # the same position, the longer one will naturally be preferred.
    for connector in sorted(connector_map, key=len, reverse=True):
        # Build a word‑boundary aware regular expression.  This prevents
        # matching connectors inside longer words (e.g. "suggest" inside
        # "suggests").  Word boundaries are added at the start and end of
        # the connector.  For multi‑word connectors, this still works as
        # long as the phrase appears as a whole between boundaries.
        pattern = r"\b" + re.escape(connector) + r"\b"
        match = re.search(pattern, lower_sentence)
        if match:
            idx = match.start()
            if (found_connector is None) or (idx < found_connector[1]):
                found_connector = (connector, idx)
    if found_connector:
        connector, idx = found_connector
        cause = original_sentence[:idx].strip().rstrip(',')
        # The connector length
        end_idx = idx + len(connector)
        effect_part = original_sentence[end_idx:].strip()
        effects = split_effects(effect_part)
        qualifiers = detect_qualifiers(original_sentence)
        # Compute probability
        prob_list = [connector_map[connector]]
        for key, val in probability_map.items():
            # match whole words or phrases in lower case
            if key in lower_sentence:
                prob_list.append(val)
        probability = 1.0
        for p in prob_list:
            probability *= p
        relation = {
            "cause": cause,
            "effects": effects,
            "probability": round(probability, 3),
            "qualifiers": qualifiers,
            "source": source_id,
        }
        relations.append(relation)
    return relations


def split_effects(effect_part: str) -> List[str]:
    """Split the effect part into individual effects.

    The function splits on commas and conjunctions ("and", "or") to produce a
    list of effects.  Leading and trailing whitespace and punctuation are
    stripped.  Empty strings are ignored.
    """
    # Replace parentheses with commas to avoid grouping
    effect_part = effect_part.replace("(", ",").replace(")", ",")
    # Replace ' and ' / ' or ' with commas
    effect_part = re.sub(r"\band\b", ",", effect_part, flags=re.IGNORECASE)
    effect_part = re.sub(r"\bor\b", ",", effect_part, flags=re.IGNORECASE)
    # Split on commas
    raw_effects = [e.strip() for e in effect_part.split(',') if e.strip()]
    return raw_effects


def generate_bnf(
    text: str,
    doc_id: str = "doc1",
    include_grammar: bool = True,
) -> str:
    """Generate a BNF representation from input text.

    Parameters
    ----------
    text : str
        Input text containing one or more sentences.
    doc_id : str, optional
        Identifier of the document.  Defaults to 'doc1'.
    include_grammar : bool, optional
        If True, the generic grammar rules for MRL are included at the top of
        the output.  Otherwise only instantiated statement rules are emitted.

    Returns
    -------
    str
        A string containing the MRL grammar in BNF.
    """
    connector_map = load_connector_mapping()
    probability_map = load_probability_mapping()
    sentences = split_sentences(text)
    all_relations: List[Dict[str, object]] = []
    for idx, sentence in enumerate(sentences, 1):
        rels = extract_relations_from_sentence(
            sentence, connector_map, probability_map, doc_id, idx
        )
        all_relations.extend(rels)
    lines: List[str] = []
    # If grammar inclusion is requested, prepend the formal MRL grammar lines.
    if include_grammar:
        lines.extend(MRL_GRAMMAR_LINES)
    # Instantiate each relation as a BNF rule
    for i, rel in enumerate(all_relations, 1):
        cause = quote_entity(rel['cause'])
        effects = ', '.join(quote_entity(e) for e in rel['effects'])
        prob = rel['probability']
        prob_str = f"[p={prob}]" if prob != 1.0 else ""
        quals = rel['qualifiers']
        if quals:
            qual_parts: List[str] = []
            for qtype, items in quals.items():
                for item in items:
                    qual_parts.append(f"{qtype}={quote_entity(item)}")
            qual_str = '[' + ', '.join(qual_parts) + ']'
        else:
            qual_str = ''
        source = rel['source']
        # Compose rule: <S_i> ::= (<cause>)->(<effects>) <prob_str> <qual_str> [source=doc1:idx]
        rule_name = f"<S{i}>"
        rule_body = f"('{cause}')->('{effects}') {prob_str} {qual_str} [source={source}]"
        lines.append(f"{rule_name} ::= {rule_body}")
    return "\n".join(lines)


def quote_entity(entity: str) -> str:
    """Quote an entity for BNF output.

    Entities may contain spaces or special characters; they are converted to
    underscores for use within angle bracketed non‑terminals or kept as
    quoted strings for terminals.  This helper simply returns the original
    string with leading/trailing whitespace stripped.  Quoting is handled by
    the caller.
    """
    return entity.strip()


#############################
# MRL concrete syntax support
#############################

# Predefined SNOMED CT URIs for common entities used in the sample.  In a real
# system these mappings would be looked up from an ontology service.  If you
# encounter a concept not in this table, a local identifier will be minted.
ENTITY_CATALOGUE: Dict[str, Tuple[str, str]] = {
    "Mechanical biliary obstruction": ("MBO", "sct:*********"),
    "Alkaline phosphatase": ("ALP", "sct:33907001"),
    "Gamma-glutamyl transferase": ("GGT", "sct:*********"),
    "Bilirubin": ("Bilirubin", "sct:88737002"),
    "Alanine transaminase": ("ALT", "sct:33908006"),
    "Aspartate transaminase": ("AST", "sct:33909003"),
    "Choledocholithiasis": ("Choledocholithiasis", "sct:84114007"),
    "Extrahepatic obstruction": ("ExtrahepaticObstruction", "sct:*********"),
    "Intrahepatic cholestasis": ("IntrahepaticCholestasis", "sct:32614006"),
    "Cholestatic picture": ("CholestaticPicture", "sct:*********"),
    "Hepatobiliary source": ("HepatobiliarySource", "local:hepatobiliary_source"),
    "Normal bilirubin": ("NormalBilirubin", "local:normal_bilirubin"),
    # Additional entities for broader MRL coverage
    "Alcoholic liver disease": ("ALD", "local:alcoholic_liver_disease"),
    "Non-alcoholic Fatty Liver Disease": ("NAFLD", "local:non_alcoholic_fatty_liver_disease"),
    "Cirrhosis": ("Cirrhosis", "sct:90708001"),
    "Vitamin B6 deficiency": ("VitaminB6Deficiency", "local:vitamin_b6_deficiency"),
    "AST:ALT ratio > 1": ("DeRitisRatioGreater1", "local:deritis_ratio_gt1"),
    "AST:ALT ratio > 2": ("DeRitisRatioGreater2", "local:deritis_ratio_gt2"),
    "AST:ALT ratio < 1": ("DeRitisRatioLess1", "local:deritis_ratio_lt1"),
}


def mint_local_finding(label: str, counter: Dict[str, int]) -> Tuple[str, str]:
    """Create a new local finding identifier and URI for a composite observation.

    Parameters
    ----------
    label : str
        Human‑readable label for the finding.
    counter : dict
        A mutable counter used to generate unique suffixes.

    Returns
    -------
    Tuple[str, str]
        A tuple containing the identifier and URI of the new finding.
    """
    idx = counter.get('finding', 0) + 1
    counter['finding'] = idx
    fid = f"F{idx}"
    uri = f"local:finding_{idx}"
    ENTITY_CATALOGUE[label] = (fid, uri)
    return fid, uri


def mint_local_property(label: str, counter: Dict[str, int]) -> Tuple[str, str]:
    """Create a new local property identifier and URI for a property or quality.

    Returns a tuple of (identifier, URI).
    """
    idx = counter.get('property', 0) + 1
    counter['property'] = idx
    pid = f"P{idx}"
    uri = f"local:property_{idx}"
    ENTITY_CATALOGUE[label] = (pid, uri)
    return pid, uri


def ensure_entity(name: str, counter: Dict[str, int]) -> Tuple[str, str, str]:
    """Ensure that an entity or finding has an identifier and URI.

    If the name exists in the catalogue it is returned; otherwise a local id
    is minted.  Returns (id, label, uri).
    """
    if name in ENTITY_CATALOGUE:
        eid, uri = ENTITY_CATALOGUE[name]
        return eid, name, uri
    # Not predefined: mint a new finding (default) and assign a URI
    fid, uri = mint_local_finding(name, counter)
    return fid, name, uri


def generate_entity_definitions() -> List[str]:
    """Generate MRL entity definition lines for all entities in the catalogue.

    Returns a list of definition statements.
    """
    definitions = []
    # Sort entities by id for consistency
    for label, (eid, uri) in sorted(ENTITY_CATALOGUE.items(), key=lambda x: x[1][0]):
        # Determine type: treat names starting with F as findings
        if eid.startswith('F'):
            etype = 'finding'
        elif eid.startswith('P'):
            etype = 'property'
        else:
            etype = 'entity'
        # Strings must be quoted according to MRL grammar
        definitions.append(f'define {etype} {eid} "{label}" <"{uri}">.')
    return definitions


def generate_mrl(text: str) -> str:
    """Translate biomedical prose into concrete MRL statements using generic
    cause‑effect extraction.

    This implementation eschews domain‑specific heuristics and instead
    leverages the generic relation extraction defined in
    :func:`extract_relations_from_sentence`.  It processes each sentence,
    identifies the earliest causal connector, splits the sentence into
    antecedent and consequent phrases, computes a probability score based on
    connector and hedging words, and produces a simple MRL assertion of the
    form ``cause influences effect``.  Each assertion is annotated with a
    default strength qualifier ("observational") and a provenance block
    pointing to the source document and sentence index.  All entities are
    resolved via :func:`ensure_entity`, minting new identifiers when not
    present in the catalogue.  The output includes entity definitions
    followed by the generated assertions.

    Parameters
    ----------
    text : str
        Input biomedical narrative.

    Returns
    -------
    str
        A multi‑line string containing MRL statements.
    """
    sentences = split_sentences(text)
    mrl_lines: List[str] = []
    counter: Dict[str, int] = {}
    ref_counter = 1
    # Track which entities are actually used in assertions so that we only
    # generate definitions for those.  Mapping from label to (id, uri).
    used_entities: Dict[str, Tuple[str, str]] = {}
    # Load connector and probability mappings used by the extractor
    connector_map = load_connector_mapping()
    probability_map = load_probability_mapping()
    # Process each sentence with the generic relation extractor
    for idx, sentence in enumerate(sentences, 1):
        relations = extract_relations_from_sentence(
            sentence,
            connector_map,
            probability_map,
            doc_id="user_text",
            sentence_index=idx,
        )
        for rel in relations:
            # Ensure cause entity
            cause_phrase = rel.get("cause", "").strip()
            if not cause_phrase:
                continue
            cause_label = cause_phrase.strip()
            cause_id, cause_label_norm, cause_uri = ensure_entity(cause_label, counter)
            # Record used entity
            used_entities[cause_label_norm] = (cause_id, cause_uri)
            # For each effect, produce an influences assertion
            for eff in rel.get("effects", []):
                effect_phrase = eff.strip()
                if not effect_phrase:
                    continue
                effect_id, effect_label_norm, effect_uri = ensure_entity(effect_phrase, counter)
                used_entities[effect_label_norm] = (effect_id, effect_uri)
                prob = rel.get("probability", 1.0)
                prob_prefix = f"{prob} :: " if prob < 1.0 else ""
                # Always include a strength qualifier; no further heuristics
                qual = '[strength="observational"]'
                prov = f'{{source:"user_text:1", ref:"{ref_counter}"}}'
                mrl_lines.append(
                    f'{prob_prefix}{cause_id} influences {effect_id} {qual} {prov}.'
                )
                ref_counter += 1
    # Prepend entity definitions
    # Generate definitions only for used entities
    defs: List[str] = []
    for label, (eid, uri) in sorted(used_entities.items(), key=lambda x: x[1][0]):
        if eid.startswith('F'):
            etype = 'finding'
        elif eid.startswith('P'):
            etype = 'property'
        else:
            etype = 'entity'
        defs.append(f'define {etype} {eid} "{label}" <"{uri}">.')
    return '\n'.join(defs + mrl_lines)


def main(argv: Optional[List[str]] = None) -> None:
    """Entry point for the script.

    Use `--mrl` as the first argument to emit MRL concrete syntax.  Without
    arguments the program reads from standard input and outputs BNF grammar.
    With a filename as the first argument it will read that file.  This
    dual behaviour supports both legacy BNF generation and the new MRL
    translation task.
    """
    if argv is None:
        argv = sys.argv[1:]
    mode = 'bnf'
    filename = None
    if argv:
        if argv[0] == '--mrl':
            mode = 'mrl'
            if len(argv) > 1:
                filename = argv[1]
        else:
            filename = argv[0]
    # Read input text
    if filename:
        with open(filename, 'r', encoding='utf-8') as f:
            text = f.read()
    else:
        text = sys.stdin.read()
    if mode == 'mrl':
        output = generate_mrl(text)
    else:
        output = generate_bnf(text, doc_id='doc1', include_grammar=True)
    print(output)


if __name__ == '__main__':
    main()