#!/usr/bin/env python3
"""
mrl_bnf_generator.py
====================

This script reads free‑form medical prose and produces a Backus–Naur Form (BNF)
representation for a simple causal knowledge representation language called
Medicak Causal‑Knowledge Representation Language (MRL).  MRL is designed as
an intermediate notation for capturing cause–effect relations extracted from
medical literature.  Each extracted statement is encoded as a BNF rule using
uniform constructs for the cause, a list of effects, optional probabilistic
confidence and contextual qualifiers, and the source of the knowledge.  The
top of the output defines the generic grammar of MRL; subsequent rules
instantiate that grammar for each statement found in the input text.

Key design choices:
* **Cause–effect relationships** – A variety of linguistic patterns such as
  "results in", "signify", "indicates" and "suggests" mark directional
  relationships.  Sentences containing "due to" are treated specially: the
  phrase following "due to" is the cause and everything after the comma is
  the effect.
* **Probability values** – Uncertainty is inherent in medicine.  The script
  assigns a baseline probability to each connector (e.g. "results in" → 0.95,
  "suggests" → 0.5) and then adjusts it using adverbs of frequency or
  modality (e.g. "usually", "often", "may") found in the same sentence.
  These mappings draw on common percentage interpretations of adverbs of
  frequency【36635478994315†L30-L44】.  When multiple uncertainty cues are
  present, their contributions are multiplied.
* **Contextual qualifiers** – Temporal cues (e.g. "gradually", "fluctuate"),
  negations (e.g. "normal") and general uncertainty words (e.g. "may",
  "tend") are collected as qualifiers.  These qualifiers are recorded in the
  output but do not alter the probability directly.
* **Multiple documents** – For every processed document, a unique identifier
  (`doc1`, `doc2`, …) is assigned.  Each statement carries a source field
  referencing its originating document and the sentence index within that
  document.  This makes it easy to trace knowledge back to its origin.

Example usage::

    python3 mrl_bnf_generator.py "path/to/text_file.txt"

If no filename is provided the script reads from standard input.

The script outputs the BNF grammar to standard output.
"""

import re
import sys
import textwrap
from typing import List, Tuple, Dict, Optional, Iterable


def load_probability_mapping() -> Dict[str, float]:
    """Return a mapping from common adverbs/verbs of frequency to probability.

    The values are approximate frequencies derived from pedagogical resources
    describing adverbs of frequency【36635478994315†L30-L44】.  Additional
    modal verbs and discourse markers are assigned reasonable default values
    reflecting their relative certainty.  These mappings may be refined or
    expanded as needed.
    """
    return {
        # Adverbs of frequency – approximate values【36635478994315†L30-L44】
        "always": 1.0,
        "usually": 0.9,
        "normally": 0.8,
        "generally": 0.8,
        "often": 0.7,
        "frequently": 0.7,
        "sometimes": 0.5,
        "occasionally": 0.3,
        "seldom": 0.1,
        "hardly ever": 0.05,
        "rarely": 0.05,
        "never": 0.0,
        # Additional uncertainty/modal verbs
        "may": 0.5,
        "might": 0.5,
        "can": 0.6,
        "could": 0.6,
        # Use only the infinitive + to forms for modal expressions to avoid
        # multiplying duplicate variants (e.g. "tend" vs "tends").  Plural
        # or singular inflections are deliberately omitted because word‑boundary
        # matching may pick them up as substrings of longer words.  Only
        # retain the forms most commonly used in biomedical texts.
        "tend to": 0.6,
        "tends to": 0.6,
        "suggests": 0.5,
        "indicates": 0.6,
        "signify": 0.8,
        "signifies": 0.8,
        "assist": 0.4,
        "assists": 0.4,
        "associated with": 0.5,
        "more likely": 0.7,
        "less likely": 0.3,
    }


def load_connector_mapping() -> Dict[str, float]:
    """Return a mapping from causal connectors to baseline probabilities.

    Connectors are phrases that explicitly convey a causal or indicative
    relationship between two propositions.  Baseline probabilities reflect
    the strength of the linguistic cue on its own.  A value of 1.0
    corresponds to a high degree of certainty, whereas lower values indicate
    weaker implications.
    """
    # When adding new connectors to this mapping, be careful to avoid
    # ambiguous words such as "cause" or "causes" that also occur as
    # nouns in general prose.  Connectors should be phrases that reliably
    # convey directional or indicative relationships.
    return {
        "results in": 0.95,
        "leads to": 0.95,
        "signify": 0.8,
        "signifies": 0.8,
        "indicates": 0.7,
        "indicate": 0.7,
        "suggests": 0.5,
        "suggest": 0.5,
        "may be associated with": 0.5,
        # Note: the bare phrase "associated with" often appears in
        # descriptive contexts (e.g. "when associated with") and is
        # therefore omitted to avoid spurious matches.  The form "may be
        # associated with" is retained as it clearly expresses a causal
        # association.
        "may assist in": 0.4,
        # "assist in" requires a preceding modal verb (e.g. may) to
        # convey uncertainty.  The bare form is excluded.
        "tend to": 0.6,
        "tends to": 0.6,
        "due to": 0.9,
    }


def split_sentences(text: str) -> List[str]:
    """Split a block of text into sentences.

    A simple heuristic is used: the text is split on periods, question marks
    and exclamation marks followed by whitespace or the end of the string.
    Abbreviations are not handled explicitly but the approach suffices for
    typical medical sentences.  Empty sentences are filtered out.
    """
    # Replace newlines with spaces
    text = text.replace("\n", " ")
    # Attempt to remove simple footnote markers (numbers surrounded by
    # spaces or enclosed in parentheses) without stripping meaningful
    # numeric information like laboratory values (e.g. 1000, 1.5).  This
    # pattern removes isolated numbers with optional punctuation when
    # they are surrounded by whitespace or brackets.  Decimal numbers
    # remain untouched.
    text = re.sub(r"\s*\(\d+\)\s*", " ", text)  # remove (1), (2) etc.
    text = re.sub(r"\s+\[\d+\]\s+", " ", text)  # remove [1], [2] etc.
    # Remove standalone footnote numbers followed by a space but keep decimals
    text = re.sub(r"(?<=\s)\d+(?=\s)", "", text)
    # Split on sentence terminators
    parts = re.split(r"(?<=[.!?])\s+", text)
    return [p.strip() for p in parts if p.strip()]


def detect_qualifiers(sentence: str) -> Dict[str, List[str]]:
    """Detect contextual qualifiers in a sentence.

    Returns a dictionary with lists of qualifiers keyed by type: 'temporal',
    'negation', and 'uncertainty'.  Qualifiers are lower‑cased but the
    original words are preserved in the output for readability.
    """
    temporal_keywords = [
        "gradually",
        "preceded",
        "fluctuate",
        "fluctuates",
        "fluctuating",
        "rise and fall",
        "peak",
        "peaked",
        "rise",
        "fall",
        "tend to fluctuate",
    ]
    negation_keywords = [
        "no",
        "not",
        "normal",
        "absence",
        "lack",
        "without",
    ]
    uncertainty_keywords = [
        "may",
        "might",
        "can",
        "could",
        "tend",
        "tends",
        "tend to",
        "tends to",
        "suggest",
        "suggests",
        "indicate",
        "indicates",
        "likely",
        "more likely",
        "less likely",
        "assist",
        "assists",
        "appear",
        "appears",
        "seem",
        "seems",
    ]

    quals: Dict[str, List[str]] = {"temporal": [], "negation": [], "uncertainty": []}
    lower = sentence.lower()
    for kw in temporal_keywords:
        if kw in lower:
            quals["temporal"].append(kw)
    for kw in negation_keywords:
        # exact match ensures we don't match 'normal' inside 'abnormal'
        pattern = r"\b" + re.escape(kw) + r"\b"
        if re.search(pattern, lower):
            quals["negation"].append(kw)
    for kw in uncertainty_keywords:
        if kw in lower:
            quals["uncertainty"].append(kw)
    return {k: v for k, v in quals.items() if v}


def extract_relations_from_sentence(
    sentence: str,
    connector_map: Dict[str, float],
    probability_map: Dict[str, float],
    doc_id: str,
    sentence_index: int,
) -> List[Dict[str, object]]:
    """Extract causal relations from a single sentence.

    Returns a list of dictionaries, each containing:
        cause (str): the antecedent phrase
        effects (list[str]): list of consequent phrases
        probability (float): combined probability score
        qualifiers (dict): contextual qualifiers
        source (str): document and sentence identifier

    The function identifies the earliest connector in the sentence and splits
    the sentence into cause and effect based on that connector.  For
    sentences beginning with "due to", the cause follows the phrase.
    """
    relations = []
    original_sentence = sentence.strip()
    lower_sentence = original_sentence.lower()
    source_id = f"{doc_id}:{sentence_index}"
    # Check for explicit "due to" constructions first
    due_match = re.search(r"due to\s+([^,]+)", lower_sentence)
    if due_match:
        cause = due_match.group(1).strip()
        # The rest of the sentence after the comma is the effect part
        # Attempt to find the first comma after "due to"
        comma_index = lower_sentence.find(',', due_match.end())
        if comma_index != -1:
            effect_part = original_sentence[comma_index + 1 :].strip()
        else:
            # No comma; effect is remainder of sentence
            effect_part = original_sentence[due_match.end() :].strip()
        effects = split_effects(effect_part)
        qualifiers = detect_qualifiers(original_sentence)
        # Base probability for 'due to'
        prob_list = [connector_map["due to"]]
        # Add probabilities for adverbs/modals found in sentence
        for key, val in probability_map.items():
            if key in lower_sentence:
                prob_list.append(val)
        probability = 1.0
        for p in prob_list:
            probability *= p
        relation = {
            "cause": cause,
            "effects": effects,
            "probability": round(probability, 3),
            "qualifiers": qualifiers,
            "source": source_id,
        }
        relations.append(relation)
        return relations

    # Look for connectors in order of appearance
    found_connector: Optional[Tuple[str, int]] = None
    # Iterate over connectors sorted by length descending to avoid partial
    # matches (e.g. "suggest" inside "suggests").  If two connectors start at
    # the same position, the longer one will naturally be preferred.
    for connector in sorted(connector_map, key=len, reverse=True):
        # Build a word‑boundary aware regular expression.  This prevents
        # matching connectors inside longer words (e.g. "suggest" inside
        # "suggests").  Word boundaries are added at the start and end of
        # the connector.  For multi‑word connectors, this still works as
        # long as the phrase appears as a whole between boundaries.
        pattern = r"\b" + re.escape(connector) + r"\b"
        match = re.search(pattern, lower_sentence)
        if match:
            idx = match.start()
            if (found_connector is None) or (idx < found_connector[1]):
                found_connector = (connector, idx)
    if found_connector:
        connector, idx = found_connector
        cause = original_sentence[:idx].strip().rstrip(',')
        # The connector length
        end_idx = idx + len(connector)
        effect_part = original_sentence[end_idx:].strip()
        effects = split_effects(effect_part)
        qualifiers = detect_qualifiers(original_sentence)
        # Compute probability
        prob_list = [connector_map[connector]]
        for key, val in probability_map.items():
            # match whole words or phrases in lower case
            if key in lower_sentence:
                prob_list.append(val)
        probability = 1.0
        for p in prob_list:
            probability *= p
        relation = {
            "cause": cause,
            "effects": effects,
            "probability": round(probability, 3),
            "qualifiers": qualifiers,
            "source": source_id,
        }
        relations.append(relation)
    return relations


def split_effects(effect_part: str) -> List[str]:
    """Split the effect part into individual effects.

    The function splits on commas and conjunctions ("and", "or") to produce a
    list of effects.  Leading and trailing whitespace and punctuation are
    stripped.  Empty strings are ignored.
    """
    # Replace parentheses with commas to avoid grouping
    effect_part = effect_part.replace("(", ",").replace(")", ",")
    # Replace ' and ' / ' or ' with commas
    effect_part = re.sub(r"\band\b", ",", effect_part, flags=re.IGNORECASE)
    effect_part = re.sub(r"\bor\b", ",", effect_part, flags=re.IGNORECASE)
    # Split on commas
    raw_effects = [e.strip() for e in effect_part.split(',') if e.strip()]
    return raw_effects


def generate_bnf(
    text: str,
    doc_id: str = "doc1",
    include_grammar: bool = True,
) -> str:
    """Generate a BNF representation from input text.

    Parameters
    ----------
    text : str
        Input text containing one or more sentences.
    doc_id : str, optional
        Identifier of the document.  Defaults to 'doc1'.
    include_grammar : bool, optional
        If True, the generic grammar rules for MRL are included at the top of
        the output.  Otherwise only instantiated statement rules are emitted.

    Returns
    -------
    str
        A string containing the MRL grammar in BNF.
    """
    connector_map = load_connector_mapping()
    probability_map = load_probability_mapping()
    sentences = split_sentences(text)
    all_relations: List[Dict[str, object]] = []
    for idx, sentence in enumerate(sentences, 1):
        rels = extract_relations_from_sentence(
            sentence, connector_map, probability_map, doc_id, idx
        )
        all_relations.extend(rels)
    lines: List[str] = []
    if include_grammar:
        lines.append("<Knowledge> ::= <Statement>*")
        lines.append("<Statement> ::= '(' <Cause> ')->(' <Effects> ')' <Probability> <Qualifiers> <Source> ')' ")
        lines.append("<Cause> ::= <Entity>")
        lines.append("<Effects> ::= <Entity> | <Entity> ',' <Effects>")
        lines.append("<Entity> ::= <Word> | <Word> '_' <Entity>")
        lines.append("<Word> ::= [A-Za-z0-9:/<>-]+")
        lines.append("<Probability> ::= '' | '[' 'p' '=' <Number> ']'")
        lines.append("<Number> ::= <Digit>+ '.' <Digit>+")
        lines.append("<Digit> ::= '0' | '1' | '2' | '3' | '4' | '5' | '6' | '7' | '8' | '9'")
        lines.append("<Qualifiers> ::= '' | '[' <QualifierList> ']'")
        lines.append("<QualifierList> ::= <Qualifier> | <Qualifier> ',' <QualifierList>")
        lines.append("<Qualifier> ::= 'temporal=' <Entity> | 'negation=' <Entity> | 'uncertainty=' <Entity>")
        lines.append("<Source> ::= '[' 'source=' <DocID> ':' <SentenceIndex> ']' ")
        lines.append("<DocID> ::= 'doc' <Digit>+")
        lines.append("<SentenceIndex> ::= <Digit>+")
        lines.append("")
    # Instantiate each relation as a BNF rule
    for i, rel in enumerate(all_relations, 1):
        cause = quote_entity(rel['cause'])
        effects = ', '.join(quote_entity(e) for e in rel['effects'])
        prob = rel['probability']
        prob_str = f"[p={prob}]" if prob != 1.0 else ""
        quals = rel['qualifiers']
        if quals:
            qual_parts: List[str] = []
            for qtype, items in quals.items():
                for item in items:
                    qual_parts.append(f"{qtype}={quote_entity(item)}")
            qual_str = '[' + ', '.join(qual_parts) + ']'
        else:
            qual_str = ''
        source = rel['source']
        # Compose rule: <S_i> ::= (<cause>)->(<effects>) <prob_str> <qual_str> [source=doc1:idx]
        rule_name = f"<S{i}>"
        rule_body = f"('{cause}')->('{effects}') {prob_str} {qual_str} [source={source}]"
        lines.append(f"{rule_name} ::= {rule_body}")
    return "\n".join(lines)


def quote_entity(entity: str) -> str:
    """Quote an entity for BNF output.

    Entities may contain spaces or special characters; they are converted to
    underscores for use within angle bracketed non‑terminals or kept as
    quoted strings for terminals.  This helper simply returns the original
    string with leading/trailing whitespace stripped.  Quoting is handled by
    the caller.
    """
    return entity.strip()


def main(argv: Optional[List[str]] = None) -> None:
    """Entry point for the script."""
    if argv is None:
        argv = sys.argv[1:]
    if argv:
        filename = argv[0]
        with open(filename, 'r', encoding='utf-8') as f:
            text = f.read()
    else:
        text = sys.stdin.read()
    bnf = generate_bnf(text, doc_id='doc1', include_grammar=True)
    print(bnf)


if __name__ == '__main__':
    main()