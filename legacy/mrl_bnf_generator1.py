#!/usr/bin/env python3
"""
mrl_bnf_generator.py
====================

This module supports two complementary tasks related to the extraction of
causal knowledge from biomedical text.

1. **MRL grammar generation (legacy)** – When invoked via its original
   entry point (`generate_bnf`), the script tokenises sentences and produces
   a Backus–Naur Form (BNF) representation for a simple causal language.  It
   identifies cause–effect patterns using basic connectors ("results in",
   "suggests", etc.), assigns rough probabilities based on hedging words like
   "usually" or "may", and annotates temporal/negation/uncertainty modifiers.
   This functionality remains for backward compatibility.

2. **MRL concrete syntax generation** – In light of the detailed
   specification for the Medical Causal‑Knowledge Representation Language
   (MRL) described in Part II of the accompanying document, the script also
   provides `generate_mrl`, a high‑level translator that converts annotated
   medical prose into fully formed MRL statements.  These statements use
   the concrete syntax defined in Chapter 5 and adhere to the semantic model
   outlined in Chapter 4.  The translator defines entities (with URIs when
   available), constructs relational assertions (e.g. `influences`,
   `has_property`, `is_evidence_for`), annotates direction, magnitude,
   modality, likelihood and strength qualifiers as required, supports an
   optional probability prefix computed from linguistic cues, and appends
   provenance blocks containing at least a source identifier and reference
   【527310129079257†L226-L241】【36635478994315†L30-L44】.

The script therefore serves as a bridge between unstructured clinical text and
the structured knowledge representation required by a causal CDSS.  When
called from the command line without arguments it reads stdin and emits BNF
rules (legacy mode).  When called with the `--mrl` flag it emits MRL
statements suitable for downstream ingestion.
"""

import re
import sys
import textwrap
from typing import List, Tuple, Dict, Optional, Iterable

###############################################################################
# MRL Grammar (Backus–Naur Form)
###############################################################################

# The following list encodes the complete BNF grammar for the Medical
# Causal‑Knowledge Representation Language (MRL) as defined in Part II of
# the specification.  The grammar is rendered as a sequence of strings so
# that it can easily be emitted from the `generate_bnf` function.  It covers
# the top‑level statement structure, entity and finding definitions,
# relational assertions, qualifier and provenance blocks, and the primitive
# lexical elements.  Comments (lines starting with `//`) mirror those in
# the document for readability.

'''
MRL_GRAMMAR_LINES: List[str] = [
    "// Top-level MRL grammar",
    "<mrl_file>          ::= (<mrl_statement> '\n')*",
    "<mrl_statement>     ::= <definition_statement> | <relation_statement>",
    "",
    "// Entity and Finding Definitions",
    "<definition_statement> ::= \"define\" <entity_type> <id> <string> \"<\" <uri> \">\"",
    "<entity_type>       ::= \"entity\" | \"finding\" | \"property\"",
    "",
    "// Relational Statements",
    "<relation_statement> ::= <prob_prefix>? <assertion> <qualifier_block>? <provenance_block>",
    "<prob_prefix>       ::= <float> \"::\"",
    "<assertion>         ::= <id> \"causes\" <id>",
    "                     | <id> \"influences\" <id> \".\" <id>",
    "                     | <id> \"is_a\" <id>",
    "                     | <id> \"has_property\" (<id> | <string>)",
    "                     | <id> \"is_evidence_for\" <id>",
    "                     | <id> \"has_temporal_relation\" <id> <string>",
    "                     | \"condition\" \"(\" <id> \" )\" \"influences\" <id> \".\" <id>",
    "                     | \"condition\" \"(\" <id> \" )\" \"has_comparative_property\" \"(\" <id> \".\" <id> \"," <id> \".\" <id> \"," <string> \" )\"",
    "",
    "// Reusable Blocks",
    "<qualifier_block>   ::= \"[\" <qualifier> (\",\" <qualifier>)* \"]\"",
    "<qualifier>         ::= <key> \"=\" (<string> | <float>)",
    "<provenance_block>  ::= \"{\" \"source:\" <source_id> (\",\" <prov_field>)* \"}\"",
    "<prov_field>        ::= \"ref:\" <string> | \"agent:\" <string> | \"timestamp:\" <string> | \"confidence:\" <float>",
    "",
    "// Primitives",
    "<id>                ::= [a-zA-Z_][a-zA-Z0-9_]*",
    "<uri>               ::= <string_literal> // A valid URI",
    "<source_id>         ::= <string_literal> // e.g., \"PMID:12345\"",
    "<string>            ::= <string_literal>",
    "<float>             ::= [0-9]+(\".\"[0-9]+)?",
    "<key>               ::= \"direction\" | \"magnitude\" | \"modality\" | \"strength\" | \"likelihood\"",
    "",
    "// Helper for string literals",
    "<string_literal>    ::= '"' (~'"')* '"'",
]
'''
# Override the grammar lines with the full BNF specification.  This list
# mirrors the grammar presented in Part II of the specification.  Each
# element corresponds to a single line of the grammar.  Quotes and
# parentheses are escaped to allow inclusion within Python strings.
MRL_GRAMMAR_LINES: List[str] = [
    "// Top-level statement",
    "<mrl_file>          ::= (<mrl_statement> '\\n')*",
    "<mrl_statement>     ::= <definition_statement> | <relation_statement>",
    "",
    "// Entity and Finding Definitions",
    "<definition_statement> ::= \"define\" <entity_type> <id> <string> \"<\" <uri> \">\"",
    "<entity_type>       ::= \"entity\" | \"finding\" | \"property\"",
    "",
    "// Relational Statements",
    "<relation_statement> ::= <prob_prefix>? <assertion> <qualifier_block>? <provenance_block>",
    "<prob_prefix>       ::= <float> \"::\"",
    "<assertion>         ::= <id> \"causes\" <id>"
    " | <id> \"influences\" <id> \".\" <id>"
    " | <id> \"is_a\" <id>"
    " | <id> \"has_property\" (<id> | <string>)"
    " | <id> \"is_evidence_for\" <id>"
    " | <id> \"has_temporal_relation\" <id> <string>"
    " | \"condition\" \"(\" <id> \" )\" \"influences\" <id> \".\" <id>"
    " | \"condition\" \"(\" <id> \" )\" \"has_comparative_property\" \"(\" <id> \".\" <id> , <id> \".\" <id> , <string> \" )\"",
    "",
    "// Reusable Blocks",
    "<qualifier_block>   ::= \"[\" <qualifier> (\",\" <qualifier>)* \"]\"",
    "<qualifier>         ::= <key> \"=\" ( <string> | <float> )",
    "<provenance_block>  ::= \"{\" \"source:\" <source_id> (\",\" <prov_field>)* \"}\"",
    "<prov_field>        ::= \"ref:\" <string> | \"agent:\" <string> | \"timestamp:\" <string> | \"confidence:\" <float>",
    "",
    "// Primitives",
    "<id>                ::= [a-zA-Z_][a-zA-Z0-9_]*",
    "<uri>               ::= <string_literal> // A valid URI",
    "<source_id>         ::= <string_literal> // e.g., \"PMID:12345\"",
    "<string>            ::= <string_literal>",
    "<float>             ::= [0-9]+(\".\"[0-9]+)?",
    "<key>               ::= \"direction\" | \"magnitude\" | \"modality\" | \"strength\" | \"likelihood\"",
    "",
    "// Helper for string literals",
    "<string_literal>    ::= '\"' (~'\"')* '\"'",
]


def load_probability_mapping() -> Dict[str, float]:
    """Return a mapping from common adverbs/verbs of frequency to probability.

    The values are approximate frequencies derived from pedagogical resources
    describing adverbs of frequency【36635478994315†L30-L44】.  Additional
    modal verbs and discourse markers are assigned reasonable default values
    reflecting their relative certainty.  These mappings may be refined or
    expanded as needed.
    """
    return {
        # Adverbs of frequency – approximate values【36635478994315†L30-L44】
        "always": 1.0,
        "usually": 0.9,
        "normally": 0.8,
        "generally": 0.8,
        "often": 0.7,
        "frequently": 0.7,
        "sometimes": 0.5,
        "occasionally": 0.3,
        "seldom": 0.1,
        "hardly ever": 0.05,
        "rarely": 0.05,
        "never": 0.0,
        # Additional uncertainty/modal verbs
        "may": 0.5,
        "might": 0.5,
        "can": 0.6,
        "could": 0.6,
        # Use only the infinitive + to forms for modal expressions to avoid
        # multiplying duplicate variants (e.g. "tend" vs "tends").  Plural
        # or singular inflections are deliberately omitted because word‑boundary
        # matching may pick them up as substrings of longer words.  Only
        # retain the forms most commonly used in biomedical texts.
        "tend to": 0.6,
        "tends to": 0.6,
        "suggests": 0.5,
        "indicates": 0.6,
        "signify": 0.8,
        "signifies": 0.8,
        "assist": 0.4,
        "assists": 0.4,
        "associated with": 0.5,
        "more likely": 0.7,
        "less likely": 0.3,
    }


def load_connector_mapping() -> Dict[str, float]:
    """Return a mapping from causal connectors to baseline probabilities.

    Connectors are phrases that explicitly convey a causal or indicative
    relationship between two propositions.  Baseline probabilities reflect
    the strength of the linguistic cue on its own.  A value of 1.0
    corresponds to a high degree of certainty, whereas lower values indicate
    weaker implications.
    """
    # When adding new connectors to this mapping, be careful to avoid
    # ambiguous words such as "cause" or "causes" that also occur as
    # nouns in general prose.  Connectors should be phrases that reliably
    # convey directional or indicative relationships.
    return {
        "results in": 0.95,
        "leads to": 0.95,
        "signify": 0.8,
        "signifies": 0.8,
        "indicates": 0.7,
        "indicate": 0.7,
        "suggests": 0.5,
        "suggest": 0.5,
        "may be associated with": 0.5,
        # Note: the bare phrase "associated with" often appears in
        # descriptive contexts (e.g. "when associated with") and is
        # therefore omitted to avoid spurious matches.  The form "may be
        # associated with" is retained as it clearly expresses a causal
        # association.
        "may assist in": 0.4,
        # "assist in" requires a preceding modal verb (e.g. may) to
        # convey uncertainty.  The bare form is excluded.
        "tend to": 0.6,
        "tends to": 0.6,
        "due to": 0.9,
    }


def split_sentences(text: str) -> List[str]:
    """Split a block of text into sentences.

    A simple heuristic is used: the text is split on periods, question marks
    and exclamation marks followed by whitespace or the end of the string.
    Abbreviations are not handled explicitly but the approach suffices for
    typical medical sentences.  Empty sentences are filtered out.
    """
    # Replace newlines with spaces
    text = text.replace("\n", " ")
    # Attempt to remove simple footnote markers (numbers surrounded by
    # spaces or enclosed in parentheses) without stripping meaningful
    # numeric information like laboratory values (e.g. 1000, 1.5).  This
    # pattern removes isolated numbers with optional punctuation when
    # they are surrounded by whitespace or brackets.  Decimal numbers
    # remain untouched.
    text = re.sub(r"\s*\(\d+\)\s*", " ", text)  # remove (1), (2) etc.
    text = re.sub(r"\s+\[\d+\]\s+", " ", text)  # remove [1], [2] etc.
    # Remove standalone footnote numbers followed by a space but keep decimals
    text = re.sub(r"(?<=\s)\d+(?=\s)", "", text)
    # Split on sentence terminators
    parts = re.split(r"(?<=[.!?])\s+", text)
    return [p.strip() for p in parts if p.strip()]


def detect_qualifiers(sentence: str) -> Dict[str, List[str]]:
    """Detect contextual qualifiers in a sentence.

    Returns a dictionary with lists of qualifiers keyed by type: 'temporal',
    'negation', and 'uncertainty'.  Qualifiers are lower‑cased but the
    original words are preserved in the output for readability.
    """
    temporal_keywords = [
        "gradually",
        "preceded",
        "fluctuate",
        "fluctuates",
        "fluctuating",
        "rise and fall",
        "peak",
        "peaked",
        "rise",
        "fall",
        "tend to fluctuate",
    ]
    negation_keywords = [
        "no",
        "not",
        "normal",
        "absence",
        "lack",
        "without",
    ]
    uncertainty_keywords = [
        "may",
        "might",
        "can",
        "could",
        "tend",
        "tends",
        "tend to",
        "tends to",
        "suggest",
        "suggests",
        "indicate",
        "indicates",
        "likely",
        "more likely",
        "less likely",
        "assist",
        "assists",
        "appear",
        "appears",
        "seem",
        "seems",
    ]

    quals: Dict[str, List[str]] = {"temporal": [], "negation": [], "uncertainty": []}
    lower = sentence.lower()
    for kw in temporal_keywords:
        if kw in lower:
            quals["temporal"].append(kw)
    for kw in negation_keywords:
        # exact match ensures we don't match 'normal' inside 'abnormal'
        pattern = r"\b" + re.escape(kw) + r"\b"
        if re.search(pattern, lower):
            quals["negation"].append(kw)
    for kw in uncertainty_keywords:
        if kw in lower:
            quals["uncertainty"].append(kw)
    return {k: v for k, v in quals.items() if v}


def extract_relations_from_sentence(
    sentence: str,
    connector_map: Dict[str, float],
    probability_map: Dict[str, float],
    doc_id: str,
    sentence_index: int,
) -> List[Dict[str, object]]:
    """Extract causal relations from a single sentence.

    Returns a list of dictionaries, each containing:
        cause (str): the antecedent phrase
        effects (list[str]): list of consequent phrases
        probability (float): combined probability score
        qualifiers (dict): contextual qualifiers
        source (str): document and sentence identifier

    The function identifies the earliest connector in the sentence and splits
    the sentence into cause and effect based on that connector.  For
    sentences beginning with "due to", the cause follows the phrase.
    """
    relations = []
    original_sentence = sentence.strip()
    lower_sentence = original_sentence.lower()
    source_id = f"{doc_id}:{sentence_index}"
    # Check for explicit "due to" constructions first
    due_match = re.search(r"due to\s+([^,]+)", lower_sentence)
    if due_match:
        cause = due_match.group(1).strip()
        # The rest of the sentence after the comma is the effect part
        # Attempt to find the first comma after "due to"
        comma_index = lower_sentence.find(',', due_match.end())
        if comma_index != -1:
            effect_part = original_sentence[comma_index + 1 :].strip()
        else:
            # No comma; effect is remainder of sentence
            effect_part = original_sentence[due_match.end() :].strip()
        effects = split_effects(effect_part)
        qualifiers = detect_qualifiers(original_sentence)
        # Base probability for 'due to'
        prob_list = [connector_map["due to"]]
        # Add probabilities for adverbs/modals found in sentence
        for key, val in probability_map.items():
            if key in lower_sentence:
                prob_list.append(val)
        probability = 1.0
        for p in prob_list:
            probability *= p
        relation = {
            "cause": cause,
            "effects": effects,
            "probability": round(probability, 3),
            "qualifiers": qualifiers,
            "source": source_id,
        }
        relations.append(relation)
        return relations

    # Look for connectors in order of appearance
    found_connector: Optional[Tuple[str, int]] = None
    # Iterate over connectors sorted by length descending to avoid partial
    # matches (e.g. "suggest" inside "suggests").  If two connectors start at
    # the same position, the longer one will naturally be preferred.
    for connector in sorted(connector_map, key=len, reverse=True):
        # Build a word‑boundary aware regular expression.  This prevents
        # matching connectors inside longer words (e.g. "suggest" inside
        # "suggests").  Word boundaries are added at the start and end of
        # the connector.  For multi‑word connectors, this still works as
        # long as the phrase appears as a whole between boundaries.
        pattern = r"\b" + re.escape(connector) + r"\b"
        match = re.search(pattern, lower_sentence)
        if match:
            idx = match.start()
            if (found_connector is None) or (idx < found_connector[1]):
                found_connector = (connector, idx)
    if found_connector:
        connector, idx = found_connector
        cause = original_sentence[:idx].strip().rstrip(',')
        # The connector length
        end_idx = idx + len(connector)
        effect_part = original_sentence[end_idx:].strip()
        effects = split_effects(effect_part)
        qualifiers = detect_qualifiers(original_sentence)
        # Compute probability
        prob_list = [connector_map[connector]]
        for key, val in probability_map.items():
            # match whole words or phrases in lower case
            if key in lower_sentence:
                prob_list.append(val)
        probability = 1.0
        for p in prob_list:
            probability *= p
        relation = {
            "cause": cause,
            "effects": effects,
            "probability": round(probability, 3),
            "qualifiers": qualifiers,
            "source": source_id,
        }
        relations.append(relation)
    return relations


def split_effects(effect_part: str) -> List[str]:
    """Split the effect part into individual effects.

    The function splits on commas and conjunctions ("and", "or") to produce a
    list of effects.  Leading and trailing whitespace and punctuation are
    stripped.  Empty strings are ignored.
    """
    # Replace parentheses with commas to avoid grouping
    effect_part = effect_part.replace("(", ",").replace(")", ",")
    # Replace ' and ' / ' or ' with commas
    effect_part = re.sub(r"\band\b", ",", effect_part, flags=re.IGNORECASE)
    effect_part = re.sub(r"\bor\b", ",", effect_part, flags=re.IGNORECASE)
    # Split on commas
    raw_effects = [e.strip() for e in effect_part.split(',') if e.strip()]
    return raw_effects


def generate_bnf(
    text: str,
    doc_id: str = "doc1",
    include_grammar: bool = True,
) -> str:
    """Generate a BNF representation from input text.

    Parameters
    ----------
    text : str
        Input text containing one or more sentences.
    doc_id : str, optional
        Identifier of the document.  Defaults to 'doc1'.
    include_grammar : bool, optional
        If True, the generic grammar rules for MRL are included at the top of
        the output.  Otherwise only instantiated statement rules are emitted.

    Returns
    -------
    str
        A string containing the MRL grammar in BNF.
    """
    connector_map = load_connector_mapping()
    probability_map = load_probability_mapping()
    sentences = split_sentences(text)
    all_relations: List[Dict[str, object]] = []
    for idx, sentence in enumerate(sentences, 1):
        rels = extract_relations_from_sentence(
            sentence, connector_map, probability_map, doc_id, idx
        )
        all_relations.extend(rels)
    lines: List[str] = []
    # If grammar inclusion is requested, prepend the formal MRL grammar lines.
    if include_grammar:
        lines.extend(MRL_GRAMMAR_LINES)
    # Instantiate each relation as a BNF rule
    for i, rel in enumerate(all_relations, 1):
        cause = quote_entity(rel['cause'])
        effects = ', '.join(quote_entity(e) for e in rel['effects'])
        prob = rel['probability']
        prob_str = f"[p={prob}]" if prob != 1.0 else ""
        quals = rel['qualifiers']
        if quals:
            qual_parts: List[str] = []
            for qtype, items in quals.items():
                for item in items:
                    qual_parts.append(f"{qtype}={quote_entity(item)}")
            qual_str = '[' + ', '.join(qual_parts) + ']'
        else:
            qual_str = ''
        source = rel['source']
        # Compose rule: <S_i> ::= (<cause>)->(<effects>) <prob_str> <qual_str> [source=doc1:idx]
        rule_name = f"<S{i}>"
        rule_body = f"('{cause}')->('{effects}') {prob_str} {qual_str} [source={source}]"
        lines.append(f"{rule_name} ::= {rule_body}")
    return "\n".join(lines)


def quote_entity(entity: str) -> str:
    """Quote an entity for BNF output.

    Entities may contain spaces or special characters; they are converted to
    underscores for use within angle bracketed non‑terminals or kept as
    quoted strings for terminals.  This helper simply returns the original
    string with leading/trailing whitespace stripped.  Quoting is handled by
    the caller.
    """
    return entity.strip()


#############################
# MRL concrete syntax support
#############################

# Predefined SNOMED CT URIs for common entities used in the sample.  In a real
# system these mappings would be looked up from an ontology service.  If you
# encounter a concept not in this table, a local identifier will be minted.
ENTITY_CATALOGUE: Dict[str, Tuple[str, str]] = {
    "Mechanical biliary obstruction": ("MBO", "sct:*********"),
    "Alkaline phosphatase": ("ALP", "sct:33907001"),
    "Gamma-glutamyl transferase": ("GGT", "sct:*********"),
    "Bilirubin": ("Bilirubin", "sct:88737002"),
    "Alanine transaminase": ("ALT", "sct:33908006"),
    "Aspartate transaminase": ("AST", "sct:33909003"),
    "Choledocholithiasis": ("Choledocholithiasis", "sct:84114007"),
    "Extrahepatic obstruction": ("ExtrahepaticObstruction", "sct:*********"),
    "Intrahepatic cholestasis": ("IntrahepaticCholestasis", "sct:32614006"),
    "Cholestatic picture": ("CholestaticPicture", "sct:*********"),
    "Hepatobiliary source": ("HepatobiliarySource", "local:hepatobiliary_source"),
    "Normal bilirubin": ("NormalBilirubin", "local:normal_bilirubin"),
}


def mint_local_finding(label: str, counter: Dict[str, int]) -> Tuple[str, str]:
    """Create a new local finding identifier and URI for a composite observation.

    Parameters
    ----------
    label : str
        Human‑readable label for the finding.
    counter : dict
        A mutable counter used to generate unique suffixes.

    Returns
    -------
    Tuple[str, str]
        A tuple containing the identifier and URI of the new finding.
    """
    idx = counter.get('finding', 0) + 1
    counter['finding'] = idx
    fid = f"F{idx}"
    uri = f"local:finding_{idx}"
    ENTITY_CATALOGUE[label] = (fid, uri)
    return fid, uri


def mint_local_property(label: str, counter: Dict[str, int]) -> Tuple[str, str]:
    """Create a new local property identifier and URI for a property or quality.

    Returns a tuple of (identifier, URI).
    """
    idx = counter.get('property', 0) + 1
    counter['property'] = idx
    pid = f"P{idx}"
    uri = f"local:property_{idx}"
    ENTITY_CATALOGUE[label] = (pid, uri)
    return pid, uri


def ensure_entity(name: str, counter: Dict[str, int]) -> Tuple[str, str, str]:
    """Ensure that an entity or finding has an identifier and URI.

    If the name exists in the catalogue it is returned; otherwise a local id
    is minted.  Returns (id, label, uri).
    """
    if name in ENTITY_CATALOGUE:
        eid, uri = ENTITY_CATALOGUE[name]
        return eid, name, uri
    # Not predefined: mint a new finding (default) and assign a URI
    fid, uri = mint_local_finding(name, counter)
    return fid, name, uri


def generate_entity_definitions() -> List[str]:
    """Generate MRL entity definition lines for all entities in the catalogue.

    Returns a list of definition statements.
    """
    definitions = []
    # Sort entities by id for consistency
    for label, (eid, uri) in sorted(ENTITY_CATALOGUE.items(), key=lambda x: x[1][0]):
        # Determine type: treat names starting with F as findings
        if eid.startswith('F'):
            etype = 'finding'
        elif eid.startswith('P'):
            etype = 'property'
        else:
            etype = 'entity'
        # Strings must be quoted according to MRL grammar
        definitions.append(f'define {etype} {eid} "{label}" <"{uri}">.')
    return definitions


def generate_mrl(text: str) -> str:
    """Translate a block of medical text into concrete MRL statements.

    This function implements a rule‑based extraction tailored to the sample
    narrative about mechanical biliary obstruction.  It illustrates how
    sentences are mapped onto MRL constructs: entities are defined (with
    SNOMED CT URIs when available); influences, property assertions,
    comparative relations, evidential links and temporal relations are
    generated; and each assertion is annotated with modality, magnitude and
    provenance.  The implementation is not a general NLP pipeline but
    demonstrates the target syntax described in Chapter 5.

    Parameters
    ----------
    text : str
        Input biomedical narrative.

    Returns
    -------
    str
        A multi‑line string containing MRL statements.
    """
    # Split sentences
    sentences = split_sentences(text)
    mrl_lines: List[str] = []
    # Counter for local definitions
    counter: Dict[str, int] = {}
    # Track last causal entity for conditional context
    last_cause_id: Optional[str] = None
    # Reference counter for provenance
    ref_counter = 1
    # Load probability and connector mappings once for probability calculation
    connector_map = load_connector_mapping()
    probability_map = load_probability_mapping()

    def compute_prob(sentence: str) -> float:
        """Compute a rough probability for a sentence using connector and
        modal vocabulary.  The function multiplies the baseline probability
        for any recognised causal connector with the first matching
        probability modifier (e.g. "usually", "may").  If no known cues
        are found the probability defaults to 1.0.  Results are rounded to
        three decimal places."""
        lower = sentence.lower()
        prob = 1.0
        # Multiply connector baseline probabilities (take first match)
        for conn, val in connector_map.items():
            if conn in lower:
                prob *= val
                break
        # Multiply with first probability word found
        for word, val in probability_map.items():
            if word in lower:
                prob *= val
                break
        return round(prob, 3)

    DEFAULT_STRENGTH = "observational"

    for sent in sentences:
        s = sent.strip()
        low = s.lower()
        # provenance information (using user_text:1 and sequential ref numbers)
        prov = f'{{source:"user_text:1", ref:"{ref_counter}"}}'
        ref_counter += 1
        # Pattern 1: results in raised levels of ...
        m = re.search(r"(.+?)\s+results in\s+raised levels of\s+(.+)", low)
        if m:
            cause_label = m.group(1).strip().rstrip('.')
            targets_part = m.group(2).rstrip('.')
            # normalise cause capitalisation
            cause_label_clean = sent[: len(m.group(1))].strip().rstrip('.')
            cause_id, _, _ = ensure_entity(cause_label_clean, counter)
            last_cause_id = cause_id
            # process targets separated by comma and 'and'
            targets = re.split(r",| and ", targets_part)
            for t in targets:
                t = t.strip()
                if not t:
                    continue
                # check for modality adjectives (e.g. 'often bilirubin')
                tokens = t.split()
                modality = None
                label_tokens: List[str] = []
                for tok in tokens:
                    if tok in probability_map:
                        modality = tok.replace(' ', '_')
                    else:
                        label_tokens.append(tok)
                target_label_clean = ' '.join(label_tokens).title()
                # convert common abbreviations to full names
                if target_label_clean.lower() == 'alp':
                    target_label_clean = 'Alkaline phosphatase'
                elif target_label_clean.lower() == 'ggt':
                    target_label_clean = 'Gamma-glutamyl transferase'
                elif target_label_clean.lower() == 'bilirubin':
                    target_label_clean = 'Bilirubin'
                tid, _, _ = ensure_entity(target_label_clean, counter)
                # Build qualifier attributes (direction always positive)
                attrs: List[str] = ["direction=\"positive\""]
                if modality:
                    attrs.append(f'modality="{modality}"')
                # Always add strength attribute
                attrs.append(f'strength="{DEFAULT_STRENGTH}"')
                attrs_str = '[' + ', '.join(attrs) + ']' if attrs else ''
                # Compute probability prefix
                prob = compute_prob(sent)
                prob_prefix = f"{prob} :: " if prob < 1.0 else ""
                mrl_lines.append(f'{prob_prefix}{cause_id} influences {tid}.level {attrs_str} {prov}.')
            continue
        # Pattern 2: ALP will usually be markedly raised in comparison with ALT
        # Recognise pattern: "<lab> ... usually ... markedly raised ... comparison with <lab2>"
        if 'markedly' in low and 'raised' in low and 'comparison' in low:
            # Extract labs
            lab_match = re.search(r'([A-Za-z/]+) .*markedly raised.*comparison with ([A-Za-z/]+)', s, re.IGNORECASE)
            if lab_match and last_cause_id:
                lab1, lab2 = lab_match.group(1), lab_match.group(2)
                lab1_clean = lab1.strip().upper()
                lab2_clean = lab2.strip().upper()
                # map abbreviations
                mapping = {'ALP': 'Alkaline phosphatase', 'ALT': 'Alanine transaminase', 'AST': 'Aspartate transaminase'}
                lab1_label = mapping.get(lab1_clean, lab1_clean.title())
                lab2_label = mapping.get(lab2_clean, lab2_clean.title())
                id1, _, _ = ensure_entity(lab1_label, counter)
                id2, _, _ = ensure_entity(lab2_label, counter)
                # Qualifiers for influences
                attrs = ['direction="positive"', 'magnitude="marked"', 'modality="usually"', f'strength="{DEFAULT_STRENGTH}"']
                attrs_str = '[' + ', '.join(attrs) + ']'
                prob = compute_prob(sent)
                prob_prefix = f"{prob} :: " if prob < 1.0 else ""
                mrl_lines.append(f'{prob_prefix}condition({last_cause_id}) influences {id1}.level {attrs_str} {prov}.')
                # Comparative property: include modality and strength as qualifiers
                comp_attrs = ['modality="usually"', f'strength="{DEFAULT_STRENGTH}"']
                comp_attrs_str = '[' + ', '.join(comp_attrs) + ']'
                mrl_lines.append(f'{prob_prefix}condition({last_cause_id}) has_comparative_property({id1}.level, {id2}.level, "marked") {comp_attrs_str} {prov}.')
            continue
        # Pattern 3: levels of ALP and GGT elevated in similar proportions signify a hepatobiliary source
        if 'elevated in similar proportions' in low and ('alp' in low and 'ggt' in low):
            # define composite finding
            fid, _, uri = ensure_entity('Proportionately elevated ALP and GGT levels', counter)
            # define hepatobiliary source entity
            hid, _, _ = ensure_entity('Hepatobiliary source', counter)
            prob = compute_prob(sent)
            prob_prefix = f"{prob} :: " if prob < 1.0 else ""
            # evidence relation with strength qualifier
            qual = f'[strength="{DEFAULT_STRENGTH}"]'
            mrl_lines.append(f'{prob_prefix}{fid} is_evidence_for {hid} {qual} {prov}.')
            continue
        # Pattern 4: due to <cause>, the levels of X and Y tend to fluctuate ... may be associated with a normal bilirubin
        if 'due to' in low and 'fluctuate' in low:
            # Extract cause after 'due to'
            cause_match = re.search(r'due to ([^,]+)', low)
            if cause_match:
                cause_phrase = cause_match.group(1).strip()
                # Map to entity representing the causal condition (e.g. Choledocholithiasis)
                cause_label = cause_phrase.title()
                cid, _, _ = ensure_entity(cause_label, counter)
                # Link the specific cause as a subtype of the last encountered cause (e.g. MBO)
                if last_cause_id:
                    # Use is_a to express that the specific condition is a type of the broader condition
                    mrl_lines.append(f'{cid} is_a {last_cause_id} {prov}.')
                last_cause_id = cid
                # Entities ALP, GGT
                alpid, _, _ = ensure_entity('Alkaline phosphatase', counter)
                ggtid, _, _ = ensure_entity('Gamma-glutamyl transferase', counter)
                # Qualifiers for fluctuating properties
                qual_attrs = [f'modality="tends_to"', f'strength="{DEFAULT_STRENGTH}"']
                qstr = '[' + ', '.join(qual_attrs) + ']'
                prob = compute_prob(sent)
                prob_prefix = f"{prob} :: " if prob < 1.0 else ""
                mrl_lines.append(f'{prob_prefix}condition({cid}) has_property({alpid}.level, "fluctuating") {qstr} {prov}.')
                mrl_lines.append(f'{prob_prefix}condition({cid}) has_property({ggtid}.level, "fluctuating") {qstr} {prov}.')
                # Normal bilirubin
                nbid, _, _ = ensure_entity('Normal bilirubin', counter)
                qual_causes = [f'modality="may"', f'strength="{DEFAULT_STRENGTH}"']
                qcauses = '[' + ', '.join(qual_causes) + ']'
                mrl_lines.append(f'{prob_prefix}condition({cid}) causes {nbid} {qcauses} {prov}.')
            continue
        # Pattern 5: Enzyme titres tend to rise and fall gradually and may be preceded by a peaked rise in liver transaminases which can reach >1000 I/U
        if 'tend to rise and fall' in low and 'preceded' in low:
            # Define findings
            # ensure_entity returns three values (id, label, uri).  We only need the id
            fid1, _, _ = ensure_entity('Peaked rise in liver transaminases', counter)
            fid2, _, _ = ensure_entity('Gradual rise and fall of enzyme titres', counter)
            prob = compute_prob(sent)
            prob_prefix = f"{prob} :: " if prob < 1.0 else ""
            # Temporal relation: peaked precedes gradual with modality and strength
            qual1 = '[' + ', '.join([f'modality="may"', f'strength="{DEFAULT_STRENGTH}"']) + ']'
            mrl_lines.append(f'{prob_prefix}has_temporal_relation({fid1}, {fid2}, "precedes") {qual1} {prov}.')
            # Property: peaked rise has level >1000 I/U (no modal qualifier but include strength)
            qual2 = '[' + f'strength="{DEFAULT_STRENGTH}"' + ']'
            mrl_lines.append(f'{prob_prefix}{fid1} has_property(level, ">1000 I/U") {qual2} {prov}.')
            continue
        # Pattern 6: The AST:ALT ratio (De Ritis ratio) may assist in differentiating the site of biliary obstruction
        if 'ratio' in low and 'assist' in low and 'differentiating' in low:
            # This sentence is handled by subsequent patterns for the specific
            # ratio thresholds (<1.5 and >1.5).  We skip it to avoid
            # generating ambiguous assertions.
            continue
        # Pattern 7: ratio <1.5 suggests extrahepatic obstruction (within a cholestatic picture)
        if 'ratio' in low and '<1.5' in low and 'suggests' in low:
            # Entities and findings
            # Unpack only the identifiers from ensure_entity (ignore label and URI)
            dr_low_id, _, _ = ensure_entity('AST:ALT ratio < 1.5', counter)
            cholestatic_id, _, _ = ensure_entity('Cholestatic picture', counter)
            extrahep_id, _, _ = ensure_entity('Extrahepatic obstruction', counter)
            prob = compute_prob(sent)
            prob_prefix = f"{prob} :: " if prob < 1.0 else ""
            qual = '[' + ', '.join([f'modality="suggests"', f'strength="{DEFAULT_STRENGTH}"']) + ']'
            mrl_lines.append(f'{prob_prefix}condition({cholestatic_id}) is_evidence_for({dr_low_id}, {extrahep_id}) {qual} {prov}.')
            last_cause_id = extrahep_id
            continue
        # Pattern 8: ALT titre is frequently considerably higher than AST
        if 'higher than' in low and 'alt' in low and 'ast' in low:
            alt_id, _, _ = ensure_entity('Alanine transaminase', counter)
            ast_id, _, _ = ensure_entity('Aspartate transaminase', counter)
            if last_cause_id:
                prob = compute_prob(sent)
                prob_prefix = f"{prob} :: " if prob < 1.0 else ""
                qual = '[' + ', '.join([f'modality="frequently"', f'strength="{DEFAULT_STRENGTH}"']) + ']'
                mrl_lines.append(f'{prob_prefix}condition({last_cause_id}) has_comparative_property({alt_id}.level, {ast_id}.level, "considerably_higher") {qual} {prov}.')
            continue
        # Pattern 9: ratio >1.5 indicates intrahepatic cholestasis is more likely
        if 'ratio' in low and '>1.5' in low and 'indicates' in low:
            # Unpack only the identifiers from ensure_entity (ignore label and URI)
            dr_high_id, _, _ = ensure_entity('AST:ALT ratio > 1.5', counter)
            cholestatic_id, _, _ = ensure_entity('Cholestatic picture', counter)
            intrahep_id, _, _ = ensure_entity('Intrahepatic cholestasis', counter)
            prob = compute_prob(sent)
            prob_prefix = f"{prob} :: " if prob < 1.0 else ""
            qual = '[' + ', '.join([f'modality="indicates"', f'likelihood="more_likely"', f'strength="{DEFAULT_STRENGTH}"']) + ']'
            mrl_lines.append(f'{prob_prefix}condition({cholestatic_id}) is_evidence_for({dr_high_id}, {intrahep_id}) {qual} {prov}.')
            continue
        # Any other sentence is ignored in this demo translation
        continue
    # Prepend entity definitions
    defs = generate_entity_definitions()
    return '\n'.join(defs + mrl_lines)


def main(argv: Optional[List[str]] = None) -> None:
    """Entry point for the script.

    Use `--mrl` as the first argument to emit MRL concrete syntax.  Without
    arguments the program reads from standard input and outputs BNF grammar.
    With a filename as the first argument it will read that file.  This
    dual behaviour supports both legacy BNF generation and the new MRL
    translation task.
    """
    if argv is None:
        argv = sys.argv[1:]
    mode = 'bnf'
    filename = None
    if argv:
        if argv[0] == '--mrl':
            mode = 'mrl'
            if len(argv) > 1:
                filename = argv[1]
        else:
            filename = argv[0]
    # Read input text
    if filename:
        with open(filename, 'r', encoding='utf-8') as f:
            text = f.read()
    else:
        text = sys.stdin.read()
    if mode == 'mrl':
        output = generate_mrl(text)
    else:
        output = generate_bnf(text, doc_id='doc1', include_grammar=True)
    print(output)


if __name__ == '__main__':
    main()